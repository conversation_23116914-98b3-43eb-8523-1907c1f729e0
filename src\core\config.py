"""
Noryon V2 - Configuration Management
Comprehensive settings for the crypto trading system
"""

import os
from typing import Dict, List, Optional, Any, Union
from pydantic import Field, validator
from pydantic_settings import BaseSettings
from decimal import Decimal
import json

class Config(BaseSettings):
    """Main configuration class for the Noryon V2 trading system"""
    
    # Environment Settings
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    DEBUG: bool = Field(default=True, env="DEBUG")
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    
    # API Server Settings
    API_HOST: str = Field(default="0.0.0.0", env="API_HOST")
    API_PORT: int = Field(default=8000, env="API_PORT")
    API_WORKERS: int = Field(default=1, env="API_WORKERS")
    
    # Database Settings
    POSTGRES_HOST: str = Field(default="localhost", env="POSTGRES_HOST")
    POSTGRES_PORT: int = Field(default=5432, env="POSTGRES_PORT")
    POSTGRES_DB: str = Field(default="noryon_trading", env="POSTGRES_DB")
    POSTGRES_USER: str = Field(default="noryon", env="POSTGRES_USER")
    POSTGRES_PASSWORD: str = Field(default="noryon123", env="POSTGRES_PASSWORD")
    
    REDIS_HOST: str = Field(default="localhost", env="REDIS_HOST")
    REDIS_PORT: int = Field(default=6379, env="REDIS_PORT")
    REDIS_DB: int = Field(default=0, env="REDIS_DB")
    REDIS_PASSWORD: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    
    CLICKHOUSE_HOST: str = Field(default="localhost", env="CLICKHOUSE_HOST")
    CLICKHOUSE_PORT: int = Field(default=8123, env="CLICKHOUSE_PORT")
    CLICKHOUSE_DB: str = Field(default="noryon_analytics", env="CLICKHOUSE_DB")
    CLICKHOUSE_USER: str = Field(default="default", env="CLICKHOUSE_USER")
    CLICKHOUSE_PASSWORD: str = Field(default="", env="CLICKHOUSE_PASSWORD")
    
    MONGODB_HOST: str = Field(default="localhost", env="MONGODB_HOST")
    MONGODB_PORT: int = Field(default=27017, env="MONGODB_PORT")
    MONGODB_DB: str = Field(default="noryon_logs", env="MONGODB_DB")
    MONGODB_USER: Optional[str] = Field(default=None, env="MONGODB_USER")
    MONGODB_PASSWORD: Optional[str] = Field(default=None, env="MONGODB_PASSWORD")
    
    # Ollama Settings
    OLLAMA_HOST: str = Field(default="localhost", env="OLLAMA_HOST")
    OLLAMA_PORT: int = Field(default=11434, env="OLLAMA_PORT")
    OLLAMA_TIMEOUT: int = Field(default=300, env="OLLAMA_TIMEOUT")
    
    # Exchange API Settings
    BINANCE_API_KEY: Optional[str] = Field(default=None, env="BINANCE_API_KEY")
    BINANCE_SECRET_KEY: Optional[str] = Field(default=None, env="BINANCE_SECRET_KEY")
    BINANCE_TESTNET: bool = Field(default=True, env="BINANCE_TESTNET")
    
    COINBASE_API_KEY: Optional[str] = Field(default=None, env="COINBASE_API_KEY")
    COINBASE_SECRET_KEY: Optional[str] = Field(default=None, env="COINBASE_SECRET_KEY")
    COINBASE_PASSPHRASE: Optional[str] = Field(default=None, env="COINBASE_PASSPHRASE")
    COINBASE_SANDBOX: bool = Field(default=True, env="COINBASE_SANDBOX")
    
    KRAKEN_API_KEY: Optional[str] = Field(default=None, env="KRAKEN_API_KEY")
    KRAKEN_SECRET_KEY: Optional[str] = Field(default=None, env="KRAKEN_SECRET_KEY")
    
    BYBIT_API_KEY: Optional[str] = Field(default=None, env="BYBIT_API_KEY")
    BYBIT_SECRET_KEY: Optional[str] = Field(default=None, env="BYBIT_SECRET_KEY")
    BYBIT_TESTNET: bool = Field(default=True, env="BYBIT_TESTNET")
    
    # Data Provider Settings
    COINGECKO_API_KEY: Optional[str] = Field(default=None, env="COINGECKO_API_KEY")
    COINMARKETCAP_API_KEY: Optional[str] = Field(default=None, env="COINMARKETCAP_API_KEY")
    ALPHA_VANTAGE_API_KEY: Optional[str] = Field(default=None, env="ALPHA_VANTAGE_API_KEY")
    NEWSAPI_KEY: Optional[str] = Field(default=None, env="NEWSAPI_KEY")
    
    # Trading Settings
    PAPER_TRADING: bool = Field(default=True, env="PAPER_TRADING")
    INITIAL_BALANCE: Decimal = Field(default=Decimal("10000"), env="INITIAL_BALANCE")
    MAX_PORTFOLIO_RISK: Decimal = Field(default=Decimal("0.02"), env="MAX_PORTFOLIO_RISK")  # 2%
    MAX_POSITION_SIZE: Decimal = Field(default=Decimal("0.1"), env="MAX_POSITION_SIZE")  # 10%
    MIN_TRADE_AMOUNT: Decimal = Field(default=Decimal("10"), env="MIN_TRADE_AMOUNT")
    
    # Risk Management
    STOP_LOSS_PERCENTAGE: Decimal = Field(default=Decimal("0.05"), env="STOP_LOSS_PERCENTAGE")  # 5%
    TAKE_PROFIT_PERCENTAGE: Decimal = Field(default=Decimal("0.15"), env="TAKE_PROFIT_PERCENTAGE")  # 15%
    MAX_DRAWDOWN: Decimal = Field(default=Decimal("0.1"), env="MAX_DRAWDOWN")  # 10%
    RISK_FREE_RATE: Decimal = Field(default=Decimal("0.02"), env="RISK_FREE_RATE")  # 2%
    
    # Trading Pairs
    TRADING_PAIRS: Union[str, List[str]] = Field(
        default=[
            "BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT", "SOL/USDT",
            "XRP/USDT", "DOT/USDT", "AVAX/USDT", "MATIC/USDT", "LINK/USDT",
            "UNI/USDT", "LTC/USDT", "BCH/USDT", "ALGO/USDT", "VET/USDT",
            "FIL/USDT", "TRX/USDT", "ETC/USDT", "XLM/USDT", "ATOM/USDT"
        ],
        env="TRADING_PAIRS"
    )
    
    # AI Model Assignments
    AI_MODELS: Dict[str, Dict[str, Any]] = Field(
        default={
            "market_watcher": {
                "model": "magistral:24b",
                "role": "Real-time market monitoring and price tracking",
                "max_tokens": 4096,
                "temperature": 0.3,
                "update_interval": 30  # seconds
            },
            "news_analyst": {
                "model": "command-r:35b",
                "role": "News sentiment analysis and market impact assessment",
                "max_tokens": 8192,
                "temperature": 0.4,
                "update_interval": 300  # 5 minutes
            },
            "technical_analyst": {
                "model": "cogito:32b",
                "role": "Technical analysis and chart pattern recognition",
                "max_tokens": 6144,
                "temperature": 0.2,
                "update_interval": 60  # 1 minute
            },
            "chief_analyst": {
                "model": "gemma3:27b",
                "role": "Strategic decision making and trade coordination",
                "max_tokens": 8192,
                "temperature": 0.3,
                "update_interval": 120  # 2 minutes
            },
            "researcher": {
                "model": "mistral-small:24b",
                "role": "Deep market research and fundamental analysis",
                "max_tokens": 12288,
                "temperature": 0.4,
                "update_interval": 900  # 15 minutes
            },
            "risk_manager": {
                "model": "falcon3:10b",
                "role": "Risk assessment and position sizing",
                "max_tokens": 4096,
                "temperature": 0.1,
                "update_interval": 60  # 1 minute
            },
            "trader": {
                "model": "granite3.3:8b",
                "role": "Trade execution and order management",
                "max_tokens": 2048,
                "temperature": 0.1,
                "update_interval": 10  # 10 seconds
            },
            "portfolio_manager": {
                "model": "qwen3:32b",
                "role": "Portfolio optimization and asset allocation",
                "max_tokens": 8192,
                "temperature": 0.2,
                "update_interval": 300  # 5 minutes
            },
            "auditor": {
                "model": "deepseek-r1:latest",
                "role": "Performance monitoring and compliance checking",
                "max_tokens": 6144,
                "temperature": 0.1,
                "update_interval": 600  # 10 minutes
            }
        }
    )
    
    # Trading Strategies
    TRADING_STRATEGIES: Dict[str, Dict[str, Any]] = Field(
        default={
            "momentum": {
                "enabled": True,
                "lookback_period": 20,
                "momentum_threshold": 0.02,
                "max_positions": 5
            },
            "mean_reversion": {
                "enabled": True,
                "lookback_period": 50,
                "deviation_threshold": 2.0,
                "max_positions": 3
            },
            "arbitrage": {
                "enabled": True,
                "min_profit_threshold": 0.005,
                "max_execution_time": 30,
                "max_positions": 10
            },
            "grid_trading": {
                "enabled": True,
                "grid_size": 0.01,
                "num_grids": 10,
                "max_positions": 5
            },
            "breakout": {
                "enabled": True,
                "lookback_period": 20,
                "volume_threshold": 1.5,
                "max_positions": 3
            }
        }
    )
    
    # Technical Indicators
    TECHNICAL_INDICATORS: Dict[str, Dict[str, Any]] = Field(
        default={
            "sma": {"periods": [20, 50, 200]},
            "ema": {"periods": [12, 26, 50]},
            "rsi": {"period": 14, "overbought": 70, "oversold": 30},
            "macd": {"fast": 12, "slow": 26, "signal": 9},
            "bollinger_bands": {"period": 20, "std_dev": 2},
            "stochastic": {"k_period": 14, "d_period": 3},
            "atr": {"period": 14},
            "volume_sma": {"period": 20},
            "obv": {"enabled": True},
            "fibonacci": {"levels": [0.236, 0.382, 0.5, 0.618, 0.786]}
        }
    )
    
    # Notification Settings
    NOTIFICATIONS: Dict[str, Dict[str, Any]] = Field(
        default={
            "discord": {
                "enabled": False,
                "webhook_url": None,
                "channels": {
                    "trades": None,
                    "alerts": None,
                    "performance": None
                }
            },
            "telegram": {
                "enabled": False,
                "bot_token": None,
                "chat_id": None
            },
            "email": {
                "enabled": False,
                "smtp_server": None,
                "smtp_port": 587,
                "username": None,
                "password": None,
                "recipients": []
            },
            "slack": {
                "enabled": False,
                "webhook_url": None,
                "channel": "#trading"
            }
        }
    )
    
    # Performance Monitoring
    MONITORING: Dict[str, Any] = Field(
        default={
            "metrics_retention_days": 90,
            "performance_calculation_interval": 3600,  # 1 hour
            "alert_thresholds": {
                "max_drawdown": 0.05,
                "daily_loss_limit": 0.02,
                "consecutive_losses": 5,
                "api_error_rate": 0.1
            },
            "backup_interval": 86400,  # 24 hours
            "log_retention_days": 30
        }
    )
    
    # Security Settings
    SECURITY: Dict[str, Any] = Field(
        default={
            "api_rate_limit": 100,  # requests per minute
            "max_concurrent_trades": 20,
            "encryption_key": None,
            "jwt_secret": None,
            "session_timeout": 3600,  # 1 hour
            "ip_whitelist": [],
            "enable_2fa": False
        }
    )
    
    @validator("TRADING_PAIRS", pre=True)
    def parse_trading_pairs(cls, v) -> List[str]:
        if isinstance(v, str):
            # Handle comma-separated string format
            if v.startswith('[') and v.endswith(']'):
                # JSON format
                return json.loads(v)
            else:
                # Comma-separated format
                return [pair.strip() for pair in v.split(',') if pair.strip()]
        elif isinstance(v, list):
            return v
        else:
            # Fallback to default
            return [
                "BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT", "SOL/USDT",
                "XRP/USDT", "DOT/USDT", "AVAX/USDT", "MATIC/USDT", "LINK/USDT"
            ]
    
    @validator("AI_MODELS", pre=True)
    def parse_ai_models(cls, v):
        if isinstance(v, str):
            return json.loads(v)
        return v
    
    @validator("TRADING_STRATEGIES", pre=True)
    def parse_trading_strategies(cls, v):
        if isinstance(v, str):
            return json.loads(v)
        return v
    
    @validator("TECHNICAL_INDICATORS", pre=True)
    def parse_technical_indicators(cls, v):
        if isinstance(v, str):
            return json.loads(v)
        return v
    
    @validator("NOTIFICATIONS", pre=True)
    def parse_notifications(cls, v):
        if isinstance(v, str):
            return json.loads(v)
        return v
    
    @validator("MONITORING", pre=True)
    def parse_monitoring(cls, v):
        if isinstance(v, str):
            return json.loads(v)
        return v
    
    @validator("SECURITY", pre=True)
    def parse_security(cls, v):
        if isinstance(v, str):
            return json.loads(v)
        return v
    
    @property
    def postgres_url(self) -> str:
        """Get PostgreSQL connection URL"""
        return f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"
    
    @property
    def redis_url(self) -> str:
        """Get Redis connection URL"""
        if self.REDIS_PASSWORD:
            return f"redis://:{self.REDIS_PASSWORD}@{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
        return f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    @property
    def clickhouse_url(self) -> str:
        """Get ClickHouse connection URL"""
        return f"clickhouse://{self.CLICKHOUSE_USER}:{self.CLICKHOUSE_PASSWORD}@{self.CLICKHOUSE_HOST}:{self.CLICKHOUSE_PORT}/{self.CLICKHOUSE_DB}"
    
    @property
    def mongodb_url(self) -> str:
        """Get MongoDB connection URL"""
        if self.MONGODB_USER and self.MONGODB_PASSWORD:
            return f"mongodb://{self.MONGODB_USER}:{self.MONGODB_PASSWORD}@{self.MONGODB_HOST}:{self.MONGODB_PORT}/{self.MONGODB_DB}"
        return f"mongodb://{self.MONGODB_HOST}:{self.MONGODB_PORT}/{self.MONGODB_DB}"
    
    @property
    def ollama_url(self) -> str:
        """Get Ollama API URL"""
        return f"http://{self.OLLAMA_HOST}:{self.OLLAMA_PORT}"
    
    def get_model_config(self, agent_name: str) -> Dict[str, Any]:
        """Get configuration for a specific AI model"""
        return self.AI_MODELS.get(agent_name, {})
    
    def get_strategy_config(self, strategy_name: str) -> Dict[str, Any]:
        """Get configuration for a specific trading strategy"""
        return self.TRADING_STRATEGIES.get(strategy_name, {})
    
    def get_indicator_config(self, indicator_name: str) -> Dict[str, Any]:
        """Get configuration for a specific technical indicator"""
        return self.TECHNICAL_INDICATORS.get(indicator_name, {})
    
    def is_exchange_configured(self, exchange_name: str) -> bool:
        """Check if an exchange is properly configured"""
        exchange_configs = {
            "binance": (self.BINANCE_API_KEY, self.BINANCE_SECRET_KEY),
            "coinbase": (self.COINBASE_API_KEY, self.COINBASE_SECRET_KEY, self.COINBASE_PASSPHRASE),
            "kraken": (self.KRAKEN_API_KEY, self.KRAKEN_SECRET_KEY),
            "bybit": (self.BYBIT_API_KEY, self.BYBIT_SECRET_KEY)
        }
        
        config = exchange_configs.get(exchange_name.lower())
        if not config:
            return False
        
        return all(value is not None for value in config)
    
    def get_enabled_exchanges(self) -> List[str]:
        """Get list of properly configured exchanges"""
        exchanges = ["binance", "coinbase", "kraken", "bybit"]
        return [exchange for exchange in exchanges if self.is_exchange_configured(exchange)]
    
    def get_enabled_strategies(self) -> List[str]:
        """Get list of enabled trading strategies"""
        return [name for name, config in self.TRADING_STRATEGIES.items() if config.get("enabled", False)]
    
    def validate_configuration(self) -> Dict[str, Any]:
        """Validate the entire configuration and return status"""
        validation_results = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "exchanges": self.get_enabled_exchanges(),
            "strategies": self.get_enabled_strategies(),
            "models": list(self.AI_MODELS.keys())
        }
        
        # Check if at least one exchange is configured
        if not validation_results["exchanges"]:
            validation_results["errors"].append("No exchanges are properly configured")
            validation_results["valid"] = False
        
        # Check if at least one strategy is enabled
        if not validation_results["strategies"]:
            validation_results["warnings"].append("No trading strategies are enabled")
        
        # Check if all required models are configured
        required_models = [
            "market_watcher", "news_analyst", "technical_analyst", "chief_analyst",
            "researcher", "risk_manager", "trader", "portfolio_manager", "auditor"
        ]
        
        missing_models = [model for model in required_models if model not in self.AI_MODELS]
        if missing_models:
            validation_results["errors"].append(f"Missing AI model configurations: {missing_models}")
            validation_results["valid"] = False
        
        # Check risk management settings
        if self.MAX_PORTFOLIO_RISK > Decimal("0.1"):
            validation_results["warnings"].append("Portfolio risk is set above 10%")
        
        if self.STOP_LOSS_PERCENTAGE > Decimal("0.2"):
            validation_results["warnings"].append("Stop loss percentage is set above 20%")
        
        return validation_results
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

# Global settings instance - lazy loaded
_settings = None

def get_settings() -> Config:
    """Get the global settings instance"""
    global _settings
    if _settings is None:
        _settings = Config()
        # Ensure logs directory exists
        os.makedirs("logs", exist_ok=True)
    return _settings

# For backward compatibility - commented out to avoid import-time errors
# settings = get_settings()