#!/usr/bin/env python3
"""
Noryon V2 - Web Dashboard
Simple web interface to monitor the trading system
"""

import asyncio
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List
import random

from fastapi import FastAP<PERSON>, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import uvicorn

# Create FastAPI app
app = FastAPI(
    title="Noryon V2 Trading Dashboard",
    description="Advanced AI Crypto Trading System Dashboard",
    version="2.0.0"
)

# Mock data for demonstration
class MockTradingData:
    def __init__(self):
        self.agents_status = {
            "market_watcher": {"status": "active", "model": "llama3.2:3b", "last_update": datetime.now()},
            "news_analyst": {"status": "active", "model": "qwen2.5:7b", "last_update": datetime.now()},
            "technical_analyst": {"status": "active", "model": "gemma2:9b", "last_update": datetime.now()},
            "chief_analyst": {"status": "active", "model": "mistral:7b", "last_update": datetime.now()},
            "researcher": {"status": "active", "model": "llama3.2:3b", "last_update": datetime.now()},
            "risk_manager": {"status": "active", "model": "qwen2.5:7b", "last_update": datetime.now()},
            "trader": {"status": "active", "model": "gemma2:9b", "last_update": datetime.now()},
            "portfolio_manager": {"status": "active", "model": "mistral:7b", "last_update": datetime.now()},
            "auditor": {"status": "active", "model": "llama3.2:3b", "last_update": datetime.now()}
        }
        
        self.portfolio = {
            "total_value": 10000.0,
            "available_balance": 2500.0,
            "total_pnl": 234.56,
            "daily_pnl": 45.23,
            "positions": [
                {"symbol": "BTC/USDT", "size": 0.15, "value": 6750.0, "pnl": 125.50},
                {"symbol": "ETH/USDT", "size": 2.5, "value": 7500.0, "pnl": 89.06},
                {"symbol": "BNB/USDT", "size": 10.0, "value": 4000.0, "pnl": 20.00}
            ]
        }
        
        self.recent_trades = [
            {"time": datetime.now() - timedelta(minutes=5), "symbol": "BTC/USDT", "side": "BUY", "amount": 0.01, "price": 45123.45, "status": "filled"},
            {"time": datetime.now() - timedelta(minutes=12), "symbol": "ETH/USDT", "side": "SELL", "amount": 0.5, "price": 3001.23, "status": "filled"},
            {"time": datetime.now() - timedelta(minutes=18), "symbol": "BNB/USDT", "side": "BUY", "amount": 2.0, "price": 399.87, "status": "filled"}
        ]
        
        self.market_data = {
            "BTC/USDT": {"price": 45123.45, "change": 2.34, "volume": 1234567890},
            "ETH/USDT": {"price": 3001.23, "change": -1.23, "volume": 987654321},
            "BNB/USDT": {"price": 399.87, "change": 0.56, "volume": 456789123},
            "ADA/USDT": {"price": 0.4567, "change": 3.45, "volume": 234567890},
            "SOL/USDT": {"price": 89.12, "change": -2.10, "volume": 345678901}
        }
        
    def update_mock_data(self):
        """Update mock data with random changes"""
        # Update market prices
        for symbol in self.market_data:
            current_price = self.market_data[symbol]["price"]
            change_percent = random.uniform(-0.05, 0.05)  # ±5% change
            new_price = current_price * (1 + change_percent)
            self.market_data[symbol]["price"] = round(new_price, 2)
            self.market_data[symbol]["change"] = round(change_percent * 100, 2)
        
        # Update portfolio value
        self.portfolio["total_value"] += random.uniform(-50, 100)
        self.portfolio["daily_pnl"] += random.uniform(-10, 20)
        
        # Update agent last update times
        for agent in self.agents_status:
            self.agents_status[agent]["last_update"] = datetime.now()

# Initialize mock data
mock_data = MockTradingData()

@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    """Main dashboard page"""
    html_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Noryon V2 Trading Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
        }
        .header {
            background: rgba(0,0,0,0.3);
            padding: 1rem 2rem;
            border-bottom: 2px solid rgba(255,255,255,0.1);
        }
        .header h1 { font-size: 2rem; margin-bottom: 0.5rem; }
        .header p { opacity: 0.8; }
        .container { padding: 2rem; }
        .grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 1.5rem; 
            margin-bottom: 2rem;
        }
        .card {
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .card h3 { margin-bottom: 1rem; color: #4fc3f7; }
        .status-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 0.5rem; }
        .agent-status {
            background: rgba(76, 175, 80, 0.2);
            padding: 0.5rem;
            border-radius: 6px;
            text-align: center;
            font-size: 0.9rem;
        }
        .metric { display: flex; justify-content: space-between; margin-bottom: 0.5rem; }
        .metric-value { font-weight: bold; color: #4fc3f7; }
        .positive { color: #4caf50; }
        .negative { color: #f44336; }
        .trade-item {
            background: rgba(255,255,255,0.05);
            padding: 0.75rem;
            border-radius: 6px;
            margin-bottom: 0.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .market-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .price { font-weight: bold; }
        .change { padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem; }
        .change.positive { background: rgba(76, 175, 80, 0.3); }
        .change.negative { background: rgba(244, 67, 54, 0.3); }
        .footer {
            text-align: center;
            padding: 2rem;
            opacity: 0.7;
            border-top: 1px solid rgba(255,255,255,0.1);
        }
        .live-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #4caf50;
            border-radius: 50%;
            animation: pulse 2s infinite;
            margin-right: 0.5rem;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Noryon V2 Trading Dashboard</h1>
        <p><span class="live-indicator"></span>Advanced AI Crypto Trading System - Live Demo Mode</p>
    </div>
    
    <div class="container">
        <div class="grid">
            <div class="card">
                <h3>🤖 AI Agents Status</h3>
                <div class="status-grid" id="agents-status">
                    <!-- Agents will be loaded here -->
                </div>
            </div>
            
            <div class="card">
                <h3>💰 Portfolio Overview</h3>
                <div id="portfolio-data">
                    <!-- Portfolio data will be loaded here -->
                </div>
            </div>
            
            <div class="card">
                <h3>📊 Market Data</h3>
                <div id="market-data">
                    <!-- Market data will be loaded here -->
                </div>
            </div>
            
            <div class="card">
                <h3>📈 Recent Trades</h3>
                <div id="recent-trades">
                    <!-- Recent trades will be loaded here -->
                </div>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>Noryon V2 - Advanced AI Crypto Trading System | Development Mode</p>
        <p>🛡️ Paper Trading Active | 🧪 Mock Data | ⚡ Real-time Updates</p>
    </div>

    <script>
        async function updateDashboard() {
            try {
                // Update agents status
                const agentsResponse = await fetch('/api/agents');
                const agentsData = await agentsResponse.json();
                const agentsContainer = document.getElementById('agents-status');
                agentsContainer.innerHTML = Object.entries(agentsData).map(([name, data]) => 
                    `<div class="agent-status">
                        <div style="font-weight: bold;">${name.replace('_', ' ').toUpperCase()}</div>
                        <div style="font-size: 0.8rem; opacity: 0.8;">${data.model}</div>
                        <div style="font-size: 0.7rem; color: #4caf50;">●ACTIVE</div>
                    </div>`
                ).join('');

                // Update portfolio
                const portfolioResponse = await fetch('/api/portfolio');
                const portfolioData = await portfolioResponse.json();
                const portfolioContainer = document.getElementById('portfolio-data');
                portfolioContainer.innerHTML = `
                    <div class="metric">
                        <span>Total Value:</span>
                        <span class="metric-value">$${portfolioData.total_value.toFixed(2)}</span>
                    </div>
                    <div class="metric">
                        <span>Available Balance:</span>
                        <span class="metric-value">$${portfolioData.available_balance.toFixed(2)}</span>
                    </div>
                    <div class="metric">
                        <span>Total P&L:</span>
                        <span class="metric-value ${portfolioData.total_pnl >= 0 ? 'positive' : 'negative'}">
                            $${portfolioData.total_pnl.toFixed(2)}
                        </span>
                    </div>
                    <div class="metric">
                        <span>Daily P&L:</span>
                        <span class="metric-value ${portfolioData.daily_pnl >= 0 ? 'positive' : 'negative'}">
                            $${portfolioData.daily_pnl.toFixed(2)}
                        </span>
                    </div>
                `;

                // Update market data
                const marketResponse = await fetch('/api/market');
                const marketData = await marketResponse.json();
                const marketContainer = document.getElementById('market-data');
                marketContainer.innerHTML = Object.entries(marketData).map(([symbol, data]) => 
                    `<div class="market-item">
                        <span>${symbol}</span>
                        <div>
                            <span class="price">$${data.price}</span>
                            <span class="change ${data.change >= 0 ? 'positive' : 'negative'}">
                                ${data.change >= 0 ? '+' : ''}${data.change.toFixed(2)}%
                            </span>
                        </div>
                    </div>`
                ).join('');

                // Update recent trades
                const tradesResponse = await fetch('/api/trades');
                const tradesData = await tradesResponse.json();
                const tradesContainer = document.getElementById('recent-trades');
                tradesContainer.innerHTML = tradesData.map(trade => 
                    `<div class="trade-item">
                        <div>
                            <div style="font-weight: bold;">${trade.symbol}</div>
                            <div style="font-size: 0.8rem; opacity: 0.8;">${new Date(trade.time).toLocaleTimeString()}</div>
                        </div>
                        <div style="text-align: right;">
                            <div class="${trade.side === 'BUY' ? 'positive' : 'negative'}">${trade.side} ${trade.amount}</div>
                            <div style="font-size: 0.8rem;">$${trade.price}</div>
                        </div>
                    </div>`
                ).join('');

            } catch (error) {
                console.error('Error updating dashboard:', error);
            }
        }

        // Update dashboard every 5 seconds
        updateDashboard();
        setInterval(updateDashboard, 5000);
    </script>
</body>
</html>
    """
    return HTMLResponse(content=html_content)

@app.get("/api/agents")
async def get_agents_status():
    """Get AI agents status"""
    return mock_data.agents_status

@app.get("/api/portfolio")
async def get_portfolio():
    """Get portfolio data"""
    return mock_data.portfolio

@app.get("/api/market")
async def get_market_data():
    """Get market data"""
    return mock_data.market_data

@app.get("/api/trades")
async def get_recent_trades():
    """Get recent trades"""
    return [
        {
            "time": trade["time"].isoformat(),
            "symbol": trade["symbol"],
            "side": trade["side"],
            "amount": trade["amount"],
            "price": trade["price"],
            "status": trade["status"]
        }
        for trade in mock_data.recent_trades
    ]

@app.get("/api/system/status")
async def get_system_status():
    """Get overall system status"""
    return {
        "status": "running",
        "mode": "development",
        "uptime": "00:05:23",
        "agents_active": len([a for a in mock_data.agents_status.values() if a["status"] == "active"]),
        "total_agents": len(mock_data.agents_status),
        "paper_trading": True,
        "last_update": datetime.now().isoformat()
    }

# Background task to update mock data
async def update_mock_data_periodically():
    """Update mock data every 10 seconds"""
    while True:
        await asyncio.sleep(10)
        mock_data.update_mock_data()

@app.on_event("startup")
async def startup_event():
    """Start background tasks"""
    asyncio.create_task(update_mock_data_periodically())

if __name__ == "__main__":
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                        NORYON V2 WEB DASHBOARD                              ║
║                      Advanced AI Crypto Trading System                      ║
║                                                                              ║
║  🌐 Web Interface: http://localhost:8000                                     ║
║  📊 Real-time Dashboard                                                      ║
║  🤖 AI Agents Monitoring                                                     ║
║  💰 Portfolio Tracking                                                       ║
║  📈 Live Market Data                                                         ║
║                                                                              ║
║  Perfect for monitoring and demonstration!                                   ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8000,
        log_level="info"
    ) 