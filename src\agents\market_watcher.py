"""
Noryon V2 - Market Watcher Agent
Real-time market monitoring and price tracking using magistral:24b model
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from decimal import Decimal

from src.agents.base_agent import BaseAgent, AgentMessage, AnalysisResult, TradingSignal
from src.core.config import Config
from src.db.database_manager import DatabaseManager

class MarketWatcherAgent(BaseAgent):
    """
    Market Watcher Agent - Real-time market monitoring and price tracking
    
    Responsibilities:
    - Monitor real-time price movements across all trading pairs
    - Detect significant price changes and volume spikes
    - Track market trends and momentum
    - Identify support and resistance levels
    - Monitor order book depth and liquidity
    - Alert other agents of important market events
    """
    
    def __init__(self, model_name: str, config: Config, db_manager: DatabaseManager):
        super().__init__("market_watcher", model_name, config, db_manager)
        
        # Market monitoring configuration
        self.price_change_threshold = 0.02  # 2% price change alert
        self.volume_spike_threshold = 2.0   # 2x average volume
        self.monitoring_interval = 30       # 30 seconds
        
        # Market data storage
        self.current_prices: Dict[str, float] = {}
        self.price_history: Dict[str, List[Dict]] = {}
        self.volume_history: Dict[str, List[float]] = {}
        self.support_resistance: Dict[str, Dict] = {}
        
        # Alert thresholds
        self.alert_thresholds = {
            'price_change_1m': 0.01,    # 1% in 1 minute
            'price_change_5m': 0.03,    # 3% in 5 minutes
            'price_change_15m': 0.05,   # 5% in 15 minutes
            'volume_spike': 2.0,        # 2x average volume
            'spread_widening': 0.005,   # 0.5% spread increase
            'liquidity_drop': 0.3       # 30% liquidity decrease
        }
        
        # Market state tracking
        self.market_state = {
            'overall_trend': 'neutral',
            'volatility_level': 'normal',
            'market_sentiment': 'neutral',
            'active_alerts': []
        }
    
    async def _initialize_agent(self):
        """Initialize market watcher specific components"""
        try:
            # Initialize price history for all trading pairs
            for symbol in self.config.TRADING_PAIRS:
                self.price_history[symbol] = []
                self.volume_history[symbol] = []
                self.support_resistance[symbol] = {
                    'support_levels': [],
                    'resistance_levels': [],
                    'last_updated': datetime.utcnow()
                }
            
            self.logger.info("✅ Market Watcher agent initialized")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Market Watcher: {e}")
            raise
    
    async def _start_agent_tasks(self) -> List[asyncio.Task]:
        """Start market watcher specific background tasks"""
        return [
            asyncio.create_task(self._monitor_market_data()),
            asyncio.create_task(self._detect_price_alerts()),
            asyncio.create_task(self._update_support_resistance()),
            asyncio.create_task(self._monitor_market_sentiment()),
            asyncio.create_task(self._broadcast_market_updates())
        ]
    
    async def _cleanup_agent(self):
        """Cleanup market watcher resources"""
        self.logger.info("🧹 Cleaning up Market Watcher resources")
    
    async def _handle_message(self, message: AgentMessage):
        """Handle incoming messages"""
        try:
            if message.message_type == "price_request":
                await self._handle_price_request(message)
            elif message.message_type == "market_status_request":
                await self._handle_market_status_request(message)
            elif message.message_type == "alert_threshold_update":
                await self._handle_alert_threshold_update(message)
            else:
                self.logger.warning(f"Unknown message type: {message.message_type}")
                
        except Exception as e:
            self.logger.error(f"Error handling message: {e}")
    
    async def _perform_periodic_analysis(self):
        """Perform periodic market analysis"""
        try:
            # Analyze overall market conditions
            market_analysis = await self._analyze_market_conditions()
            
            # Update market state
            await self._update_market_state(market_analysis)
            
            # Generate market insights
            insights = await self._generate_market_insights()
            
            # Broadcast insights to other agents
            await self._broadcast_insights(insights)
            
        except Exception as e:
            self.logger.error(f"Error in periodic analysis: {e}")
    
    # Market Monitoring Tasks
    
    async def _monitor_market_data(self):
        """Monitor real-time market data"""
        while self.running:
            try:
                for symbol in self.config.TRADING_PAIRS:
                    # Get current market data (this would come from exchange manager)
                    market_data = await self._get_market_data(symbol)
                    
                    if market_data:
                        await self._process_market_data(symbol, market_data)
                
                await asyncio.sleep(self.monitoring_interval)
                
            except Exception as e:
                self.logger.error(f"Error monitoring market data: {e}")
                await asyncio.sleep(self.monitoring_interval)
    
    async def _detect_price_alerts(self):
        """Detect significant price movements and generate alerts"""
        while self.running:
            try:
                for symbol in self.config.TRADING_PAIRS:
                    alerts = await self._check_price_alerts(symbol)
                    
                    for alert in alerts:
                        await self._send_price_alert(symbol, alert)
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                self.logger.error(f"Error detecting price alerts: {e}")
                await asyncio.sleep(10)
    
    async def _update_support_resistance(self):
        """Update support and resistance levels"""
        while self.running:
            try:
                for symbol in self.config.TRADING_PAIRS:
                    await self._calculate_support_resistance(symbol)
                
                await asyncio.sleep(300)  # Update every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Error updating support/resistance: {e}")
                await asyncio.sleep(300)
    
    async def _monitor_market_sentiment(self):
        """Monitor overall market sentiment"""
        while self.running:
            try:
                sentiment = await self._analyze_market_sentiment()
                self.market_state['market_sentiment'] = sentiment
                
                await asyncio.sleep(120)  # Update every 2 minutes
                
            except Exception as e:
                self.logger.error(f"Error monitoring market sentiment: {e}")
                await asyncio.sleep(120)
    
    async def _broadcast_market_updates(self):
        """Broadcast market updates to other agents"""
        while self.running:
            try:
                market_update = await self._prepare_market_update()
                
                # Send to all relevant agents
                agents = ['technical_analyst', 'chief_analyst', 'risk_manager', 'trader']
                for agent in agents:
                    await self.send_message(
                        target=agent,
                        message_type="market_update",
                        content=market_update,
                        priority=2
                    )
                
                await asyncio.sleep(60)  # Broadcast every minute
                
            except Exception as e:
                self.logger.error(f"Error broadcasting market updates: {e}")
                await asyncio.sleep(60)
    
    # Analysis Methods
    
    async def _analyze_market_conditions(self) -> Dict[str, Any]:
        """Analyze overall market conditions using AI"""
        try:
            # Prepare market data for analysis
            market_data = {
                'symbols': self.config.TRADING_PAIRS,
                'current_prices': self.current_prices,
                'price_changes': await self._calculate_price_changes(),
                'volume_data': await self._get_volume_summary(),
                'market_state': self.market_state,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            # Perform AI analysis
            analysis = await self.analyze(market_data, "market_conditions")
            
            return analysis.result
            
        except Exception as e:
            self.logger.error(f"Error analyzing market conditions: {e}")
            return {}
    
    async def _generate_market_insights(self) -> Dict[str, Any]:
        """Generate market insights using AI"""
        try:
            # Prepare data for insight generation
            insight_data = {
                'price_history': self._get_recent_price_history(),
                'volume_patterns': self._get_volume_patterns(),
                'support_resistance': self.support_resistance,
                'market_alerts': self.market_state['active_alerts'],
                'timestamp': datetime.utcnow().isoformat()
            }
            
            # Generate insights using AI
            analysis = await self.analyze(insight_data, "market_insights")
            
            return analysis.result
            
        except Exception as e:
            self.logger.error(f"Error generating market insights: {e}")
            return {}
    
    async def _analyze_market_sentiment(self) -> str:
        """Analyze overall market sentiment"""
        try:
            # Calculate sentiment indicators
            price_changes = await self._calculate_price_changes()
            volume_changes = await self._calculate_volume_changes()
            
            # Count positive vs negative movements
            positive_moves = sum(1 for change in price_changes.values() if change > 0)
            negative_moves = sum(1 for change in price_changes.values() if change < 0)
            total_moves = len(price_changes)
            
            if total_moves == 0:
                return 'neutral'
            
            positive_ratio = positive_moves / total_moves
            
            # Determine sentiment
            if positive_ratio > 0.7:
                return 'bullish'
            elif positive_ratio < 0.3:
                return 'bearish'
            else:
                return 'neutral'
                
        except Exception as e:
            self.logger.error(f"Error analyzing market sentiment: {e}")
            return 'neutral'
    
    # Data Processing Methods
    
    async def _process_market_data(self, symbol: str, market_data: Dict[str, Any]):
        """Process incoming market data for a symbol"""
        try:
            current_price = market_data.get('price', 0)
            volume = market_data.get('volume', 0)
            timestamp = datetime.utcnow()
            
            # Update current price
            previous_price = self.current_prices.get(symbol, current_price)
            self.current_prices[symbol] = current_price
            
            # Add to price history
            price_entry = {
                'price': current_price,
                'volume': volume,
                'timestamp': timestamp,
                'change': (current_price - previous_price) / previous_price if previous_price > 0 else 0
            }
            
            self.price_history[symbol].append(price_entry)
            
            # Keep only recent history (last 24 hours)
            cutoff_time = timestamp - timedelta(hours=24)
            self.price_history[symbol] = [
                entry for entry in self.price_history[symbol]
                if entry['timestamp'] > cutoff_time
            ]
            
            # Update volume history
            self.volume_history[symbol].append(volume)
            if len(self.volume_history[symbol]) > 1440:  # Keep 24 hours of minute data
                self.volume_history[symbol] = self.volume_history[symbol][-1440:]
            
            # Store in database
            await self._store_market_data(symbol, market_data)
            
        except Exception as e:
            self.logger.error(f"Error processing market data for {symbol}: {e}")
    
    async def _check_price_alerts(self, symbol: str) -> List[Dict[str, Any]]:
        """Check for price alerts for a symbol"""
        alerts = []
        
        try:
            if symbol not in self.price_history or len(self.price_history[symbol]) < 2:
                return alerts
            
            current_data = self.price_history[symbol][-1]
            current_price = current_data['price']
            
            # Check various timeframe alerts
            timeframes = {
                '1m': 60,
                '5m': 300,
                '15m': 900
            }
            
            for timeframe, seconds in timeframes.items():
                historical_data = [
                    entry for entry in self.price_history[symbol]
                    if (datetime.utcnow() - entry['timestamp']).total_seconds() <= seconds
                ]
                
                if len(historical_data) >= 2:
                    old_price = historical_data[0]['price']
                    price_change = abs(current_price - old_price) / old_price
                    
                    threshold_key = f'price_change_{timeframe}'
                    if price_change >= self.alert_thresholds.get(threshold_key, 0.05):
                        alerts.append({
                            'type': 'price_change',
                            'timeframe': timeframe,
                            'change_percent': price_change,
                            'direction': 'up' if current_price > old_price else 'down',
                            'current_price': current_price,
                            'previous_price': old_price,
                            'timestamp': datetime.utcnow()
                        })
            
            # Check volume spike
            if len(self.volume_history[symbol]) >= 20:
                current_volume = current_data['volume']
                avg_volume = sum(self.volume_history[symbol][-20:]) / 20
                
                if current_volume > avg_volume * self.alert_thresholds['volume_spike']:
                    alerts.append({
                        'type': 'volume_spike',
                        'current_volume': current_volume,
                        'average_volume': avg_volume,
                        'spike_ratio': current_volume / avg_volume,
                        'timestamp': datetime.utcnow()
                    })
            
        except Exception as e:
            self.logger.error(f"Error checking price alerts for {symbol}: {e}")
        
        return alerts
    
    async def _calculate_support_resistance(self, symbol: str):
        """Calculate support and resistance levels"""
        try:
            if symbol not in self.price_history or len(self.price_history[symbol]) < 50:
                return
            
            # Get recent price data
            prices = [entry['price'] for entry in self.price_history[symbol][-200:]]
            
            # Simple support/resistance calculation using local minima/maxima
            support_levels = []
            resistance_levels = []
            
            for i in range(2, len(prices) - 2):
                # Local minimum (support)
                if (prices[i] < prices[i-1] and prices[i] < prices[i-2] and 
                    prices[i] < prices[i+1] and prices[i] < prices[i+2]):
                    support_levels.append(prices[i])
                
                # Local maximum (resistance)
                if (prices[i] > prices[i-1] and prices[i] > prices[i-2] and 
                    prices[i] > prices[i+1] and prices[i] > prices[i+2]):
                    resistance_levels.append(prices[i])
            
            # Keep only the most significant levels
            support_levels = sorted(set(support_levels))[-5:]  # Top 5 support levels
            resistance_levels = sorted(set(resistance_levels), reverse=True)[:5]  # Top 5 resistance levels
            
            self.support_resistance[symbol] = {
                'support_levels': support_levels,
                'resistance_levels': resistance_levels,
                'last_updated': datetime.utcnow()
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating support/resistance for {symbol}: {e}")
    
    # AI Prompt Methods
    
    async def _prepare_analysis_prompt(self, data: Dict[str, Any], analysis_type: str) -> str:
        """Prepare analysis prompt for the AI model"""
        if analysis_type == "market_conditions":
            return f"""
            As an expert market analyst, analyze the current cryptocurrency market conditions based on the following data:

            Trading Pairs: {', '.join(data['symbols'])}
            Current Prices: {json.dumps(data['current_prices'], indent=2)}
            Price Changes: {json.dumps(data['price_changes'], indent=2)}
            Volume Data: {json.dumps(data['volume_data'], indent=2)}
            Current Market State: {json.dumps(data['market_state'], indent=2)}
            Timestamp: {data['timestamp']}

            Please provide a comprehensive analysis including:
            1. Overall market trend (bullish/bearish/neutral)
            2. Volatility assessment (low/normal/high)
            3. Key market drivers
            4. Risk factors to watch
            5. Notable price movements
            6. Volume analysis
            7. Market outlook for the next few hours

            Respond in JSON format with the following structure:
            {{
                "overall_trend": "bullish/bearish/neutral",
                "volatility_level": "low/normal/high",
                "confidence": 0.0-1.0,
                "key_drivers": ["driver1", "driver2"],
                "risk_factors": ["risk1", "risk2"],
                "notable_movements": [{{
                    "symbol": "BTC/USDT",
                    "movement": "description",
                    "significance": "high/medium/low"
                }}],
                "volume_analysis": "description",
                "short_term_outlook": "description",
                "reasoning": "detailed explanation"
            }}
            """
        
        elif analysis_type == "market_insights":
            return f"""
            As an expert market analyst, generate actionable insights from the following market data:

            Recent Price History: {json.dumps(data['price_history'], indent=2)}
            Volume Patterns: {json.dumps(data['volume_patterns'], indent=2)}
            Support/Resistance Levels: {json.dumps(data['support_resistance'], indent=2)}
            Active Market Alerts: {json.dumps(data['market_alerts'], indent=2)}
            Timestamp: {data['timestamp']}

            Please provide insights including:
            1. Key patterns identified
            2. Trading opportunities
            3. Risk warnings
            4. Market structure analysis
            5. Momentum indicators
            6. Actionable recommendations

            Respond in JSON format with the following structure:
            {{
                "key_patterns": ["pattern1", "pattern2"],
                "trading_opportunities": [{{
                    "symbol": "BTC/USDT",
                    "opportunity": "description",
                    "confidence": 0.0-1.0,
                    "timeframe": "short/medium/long"
                }}],
                "risk_warnings": ["warning1", "warning2"],
                "market_structure": "description",
                "momentum_indicators": {{
                    "overall": "bullish/bearish/neutral",
                    "strength": 0.0-1.0
                }},
                "recommendations": ["rec1", "rec2"],
                "confidence": 0.0-1.0,
                "reasoning": "detailed explanation"
            }}
            """
        
        return ""
    
    async def _parse_analysis_response(self, response: str, analysis_type: str) -> Dict[str, Any]:
        """Parse analysis response from AI model"""
        try:
            # Try to parse JSON response
            result = json.loads(response)
            
            # Validate required fields
            if analysis_type == "market_conditions":
                required_fields = ['overall_trend', 'volatility_level', 'confidence', 'reasoning']
            elif analysis_type == "market_insights":
                required_fields = ['key_patterns', 'confidence', 'reasoning']
            else:
                required_fields = ['confidence', 'reasoning']
            
            for field in required_fields:
                if field not in result:
                    result[field] = "unknown" if field != 'confidence' else 0.5
            
            return result
            
        except json.JSONDecodeError:
            # Fallback parsing if JSON fails
            return {
                'confidence': 0.3,
                'reasoning': response[:500],
                'error': 'Failed to parse JSON response'
            }
    
    async def _prepare_signal_prompt(self, analysis_data: Dict[str, Any]) -> str:
        """Prepare signal generation prompt"""
        return f"""
        Based on the market analysis data, determine if any immediate trading signals should be generated:

        Analysis Data: {json.dumps(analysis_data, indent=2)}

        Consider:
        1. Significant price movements
        2. Volume spikes
        3. Support/resistance breaks
        4. Market momentum changes

        Respond in JSON format:
        {{
            "action": "buy/sell/hold",
            "strength": 0.0-1.0,
            "confidence": 0.0-1.0,
            "reasoning": "explanation",
            "urgency": "low/medium/high"
        }}
        """
    
    async def _parse_signal_response(self, response: str) -> Dict[str, Any]:
        """Parse signal response from AI model"""
        try:
            result = json.loads(response)
            
            # Validate and set defaults
            result.setdefault('action', 'hold')
            result.setdefault('strength', 0.5)
            result.setdefault('confidence', 0.5)
            result.setdefault('reasoning', '')
            
            return result
            
        except json.JSONDecodeError:
            return {
                'action': 'hold',
                'strength': 0.0,
                'confidence': 0.0,
                'reasoning': 'Failed to parse signal response'
            }
    
    # Helper Methods
    
    async def _get_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get market data for a symbol (placeholder - would integrate with exchange manager)"""
        # This would be replaced with actual exchange manager integration
        return {
            'symbol': symbol,
            'price': 50000.0,  # Placeholder
            'volume': 1000000.0,
            'bid': 49995.0,
            'ask': 50005.0,
            'timestamp': datetime.utcnow()
        }
    
    async def _calculate_price_changes(self) -> Dict[str, float]:
        """Calculate price changes for all symbols"""
        changes = {}
        
        for symbol in self.config.TRADING_PAIRS:
            if symbol in self.price_history and len(self.price_history[symbol]) >= 2:
                current = self.price_history[symbol][-1]['price']
                previous = self.price_history[symbol][-2]['price']
                changes[symbol] = (current - previous) / previous if previous > 0 else 0
            else:
                changes[symbol] = 0.0
        
        return changes
    
    async def _calculate_volume_changes(self) -> Dict[str, float]:
        """Calculate volume changes for all symbols"""
        changes = {}
        
        for symbol in self.config.TRADING_PAIRS:
            if symbol in self.volume_history and len(self.volume_history[symbol]) >= 20:
                current = self.volume_history[symbol][-1]
                average = sum(self.volume_history[symbol][-20:]) / 20
                changes[symbol] = (current - average) / average if average > 0 else 0
            else:
                changes[symbol] = 0.0
        
        return changes
    
    async def _get_volume_summary(self) -> Dict[str, Any]:
        """Get volume summary for all symbols"""
        summary = {}
        
        for symbol in self.config.TRADING_PAIRS:
            if symbol in self.volume_history and self.volume_history[symbol]:
                volumes = self.volume_history[symbol]
                summary[symbol] = {
                    'current': volumes[-1] if volumes else 0,
                    'average_1h': sum(volumes[-60:]) / len(volumes[-60:]) if len(volumes) >= 60 else 0,
                    'average_24h': sum(volumes) / len(volumes) if volumes else 0
                }
            else:
                summary[symbol] = {'current': 0, 'average_1h': 0, 'average_24h': 0}
        
        return summary
    
    def _get_recent_price_history(self) -> Dict[str, List[Dict]]:
        """Get recent price history for analysis"""
        recent_history = {}
        
        for symbol in self.config.TRADING_PAIRS:
            if symbol in self.price_history:
                # Get last 100 entries
                recent_history[symbol] = self.price_history[symbol][-100:]
            else:
                recent_history[symbol] = []
        
        return recent_history
    
    def _get_volume_patterns(self) -> Dict[str, Any]:
        """Get volume patterns for analysis"""
        patterns = {}
        
        for symbol in self.config.TRADING_PAIRS:
            if symbol in self.volume_history and len(self.volume_history[symbol]) >= 20:
                volumes = self.volume_history[symbol][-20:]
                patterns[symbol] = {
                    'trend': 'increasing' if volumes[-1] > volumes[0] else 'decreasing',
                    'volatility': max(volumes) / min(volumes) if min(volumes) > 0 else 1,
                    'average': sum(volumes) / len(volumes)
                }
            else:
                patterns[symbol] = {'trend': 'stable', 'volatility': 1, 'average': 0}
        
        return patterns
    
    async def _update_market_state(self, analysis: Dict[str, Any]):
        """Update market state based on analysis"""
        self.market_state.update({
            'overall_trend': analysis.get('overall_trend', 'neutral'),
            'volatility_level': analysis.get('volatility_level', 'normal'),
            'last_analysis': datetime.utcnow()
        })
    
    async def _prepare_market_update(self) -> Dict[str, Any]:
        """Prepare market update for broadcasting"""
        return {
            'timestamp': datetime.utcnow().isoformat(),
            'current_prices': self.current_prices,
            'market_state': self.market_state,
            'support_resistance': self.support_resistance,
            'active_alerts': self.market_state.get('active_alerts', []),
            'price_changes': await self._calculate_price_changes()
        }
    
    async def _broadcast_insights(self, insights: Dict[str, Any]):
        """Broadcast market insights to other agents"""
        if insights:
            await self.send_message(
                target="chief_analyst",
                message_type="market_insights",
                content=insights,
                priority=2
            )
    
    async def _send_price_alert(self, symbol: str, alert: Dict[str, Any]):
        """Send price alert to relevant agents"""
        alert_message = {
            'symbol': symbol,
            'alert': alert,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Send to risk manager for immediate attention
        await self.send_message(
            target="risk_manager",
            message_type="price_alert",
            content=alert_message,
            priority=3
        )
        
        # Send to chief analyst for strategic consideration
        await self.send_message(
            target="chief_analyst",
            message_type="price_alert",
            content=alert_message,
            priority=2
        )
    
    async def _store_market_data(self, symbol: str, market_data: Dict[str, Any]):
        """Store market data in database"""
        try:
            # Store in ClickHouse for analytics
            tick_data = {
                'timestamp': datetime.utcnow(),
                'symbol': symbol,
                'price': market_data.get('price', 0),
                'volume': market_data.get('volume', 0),
                'bid': market_data.get('bid'),
                'ask': market_data.get('ask'),
                'exchange': market_data.get('exchange', 'aggregated')
            }
            
            await self.db_manager.insert_price_tick(tick_data)
            
        except Exception as e:
            self.logger.error(f"Error storing market data: {e}")
    
    # Message Handlers
    
    async def _handle_price_request(self, message: AgentMessage):
        """Handle price request from other agents"""
        symbol = message.content.get('symbol')
        
        if symbol and symbol in self.current_prices:
            response = {
                'symbol': symbol,
                'price': self.current_prices[symbol],
                'timestamp': datetime.utcnow().isoformat(),
                'support_resistance': self.support_resistance.get(symbol, {})
            }
            
            await self.send_message(
                target=message.source,
                message_type="price_response",
                content=response,
                correlation_id=message.correlation_id
            )
    
    async def _handle_market_status_request(self, message: AgentMessage):
        """Handle market status request"""
        status = {
            'market_state': self.market_state,
            'monitored_symbols': len(self.current_prices),
            'active_alerts': len(self.market_state.get('active_alerts', [])),
            'last_update': datetime.utcnow().isoformat()
        }
        
        await self.send_message(
            target=message.source,
            message_type="market_status_response",
            content=status,
            correlation_id=message.correlation_id
        )
    
    async def _handle_alert_threshold_update(self, message: AgentMessage):
        """Handle alert threshold update"""
        new_thresholds = message.content.get('thresholds', {})
        self.alert_thresholds.update(new_thresholds)
        
        self.logger.info(f"Updated alert thresholds: {new_thresholds}")
        
        # Acknowledge update
        await self.send_message(
            target=message.source,
            message_type="threshold_update_ack",
            content={'status': 'updated'},
            correlation_id=message.correlation_id
        ) 