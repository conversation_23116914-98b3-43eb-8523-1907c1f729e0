"""ClickHouse client utilities for Sprint-0.

Reference: https://clickhouse-driver.readthedocs.io/en/latest/quickstart.html
"""

from __future__ import annotations

import logging
from typing import Any, Dict

from clickhouse_driver import Client

from src.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

# Client is created lazily to avoid connection at import time in unit tests.
_client: Client | None = None


def get_client() -> Client:
    """Return a singleton ClickHouse Client instance."""
    global _client
    if _client is None:
        logger.info("Creating ClickHouse client …")
        _client = Client(
            host=settings.CLICKHOUSE_HOST,
            port=settings.CLICKHOUSE_PORT,
            user=settings.CLICKHOUSE_USER,
            password=settings.CLICKHOUSE_PASSWORD or "",
            database=settings.CLICKHOUSE_DB,
            settings={"use_numpy": False},
        )
    return _client


def ping() -> bool:
    """Simple ping returning True if <PERSON>lickHouse responds to SELECT 1."""
    client = get_client()
    try:
        result = client.execute("SELECT 1")
        return result == [(1,)]
    except Exception as exc:  # pylint: disable=broad-except
        logger.error("ClickHouse ping failed: %s", exc)
        return False


def insert_tick(row: Dict[str, Any]) -> None:
    """Insert single tick row into `ticks` table.

    Args:
        row: Mapping with keys symbol, exchange, ts, bid, ask, last.

    Note: No extensive validation here; rely on upstream producers.
    """
    client = get_client()
    client.execute("INSERT INTO ticks VALUES", [row]) 