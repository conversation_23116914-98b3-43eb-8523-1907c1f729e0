"""ClickHouse client utilities for Sprint-0.

Reference: https://clickhouse-driver.readthedocs.io/en/latest/quickstart.html
"""

from __future__ import annotations

import logging
from typing import Any, Dict

from clickhouse_driver import Client

from src.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

# Client is created lazily to avoid connection at import time in unit tests.
_client: Client | None = None


class MockClickHouseClient:
    """Mock ClickHouse client for when ClickHouse is not available"""

    def execute(self, query: str, params=None):
        """Mock execute method"""
        if "SELECT 1" in query:
            return [(1,)]
        elif "CREATE TABLE" in query or "CREATE MATERIALIZED VIEW" in query:
            return []
        elif "INSERT INTO" in query:
            return []
        elif "SELECT" in query:
            return []
        return []

def get_client() -> Client:
    """Return a singleton ClickHouse Client instance."""
    global _client
    if _client is None:
        logger.info("Creating ClickHouse client …")
        try:
            _client = Client(
                host=settings.CLICKHOUSE_HOST,
                port=settings.CLICKHOUSE_PORT,
                user=settings.CLICKHOUSE_USER,
                password=settings.CLICKHOUSE_PASSWORD or "",
                database=settings.CLICKHOUSE_DB,
                settings={"use_numpy": False},
            )
            # Test the connection
            _client.execute("SELECT 1")
            logger.info("✅ ClickHouse connection successful")
        except Exception as e:
            logger.warning(f"⚠️ ClickHouse not available, using mock client: {e}")
            _client = MockClickHouseClient()
    return _client


def ping() -> bool:
    """Simple ping returning True if ClickHouse responds to SELECT 1."""
    client = get_client()
    try:
        result = client.execute("SELECT 1")
        return result == [(1,)]
    except Exception as exc:  # pylint: disable=broad-except
        logger.error("ClickHouse ping failed: %s", exc)
        return False


def insert_tick(row: Dict[str, Any]) -> None:
    """Insert single tick row into `ticks` table.

    Args:
        row: Mapping with keys symbol, exchange, ts, bid, ask, last.

    Note: No extensive validation here; rely on upstream producers.
    """
    client = get_client()
    client.execute("INSERT INTO ticks VALUES", [row]) 