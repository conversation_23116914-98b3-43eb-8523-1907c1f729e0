# Noryon V2 - Advanced AI Crypto Trading System

<div align="center">

![Noryon V2 Logo](https://img.shields.io/badge/Noryon-V2-blue?style=for-the-badge&logo=bitcoin&logoColor=white)

[![Python](https://img.shields.io/badge/Python-3.9+-blue?style=flat-square&logo=python)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green?style=flat-square&logo=fastapi)](https://fastapi.tiangolo.com)
[![Docker](https://img.shields.io/badge/Docker-Compose-blue?style=flat-square&logo=docker)](https://docker.com)
[![License](https://img.shields.io/badge/License-MIT-yellow?style=flat-square)](LICENSE)

**🤖 9 Specialized AI Agents • 📊 Real-time Analysis • ⚡ Automated Trading • 🛡️ Risk Management**

</div>

---

## 🌟 Overview

Noryon V2 is a state-of-the-art cryptocurrency trading system powered by 9 specialized AI agents using your local Ollama models. The system provides real-time market analysis, automated trading strategies, advanced risk management, and comprehensive portfolio optimization.

### 🎯 Key Features

- **🤖 9 Specialized AI Agents** - Each with unique roles and responsibilities
- **📊 Real-time Market Analysis** - Continuous monitoring across multiple exchanges
- **⚡ Automated Trading Strategies** - Momentum, arbitrage, grid trading, and more
- **🛡️ Advanced Risk Management** - Multi-layer protection and position sizing
- **📈 Portfolio Optimization** - Dynamic rebalancing and performance tracking
- **🔄 Multi-Exchange Support** - Binance, Coinbase, Kraken, Bybit
- **📱 Real-time Notifications** - Discord, Telegram, Slack, Email alerts
- **📊 Comprehensive Monitoring** - Grafana dashboards and Prometheus metrics
- **🔒 Security First** - Encrypted credentials and secure API handling

---

## 🤖 AI Agent Architecture

### Agent Roles & Models

| Agent | Model | Role | Responsibilities |
|-------|-------|------|------------------|
| **Market Watcher** | `magistral:24b` | 👁️ Real-time Monitor | Price tracking, volume analysis, market alerts |
| **News Analyst** | `command-r:35b` | 📰 Information Processor | News sentiment, social media analysis |
| **Technical Analyst** | `cogito:32b` | 📊 Chart Expert | Technical indicators, pattern recognition |
| **Chief Analyst** | `gemma3:27b` | 🧠 Strategic Coordinator | Decision synthesis, strategy coordination |
| **Researcher** | `mistral-small:24b` | 🔬 Deep Investigator | Fundamental analysis, market research |
| **Risk Manager** | `falcon3:10b` | 🛡️ Protection Specialist | Risk assessment, position sizing |
| **Trader** | `granite3.3:8b` | 💼 Execution Expert | Order management, trade execution |
| **Portfolio Manager** | `qwen3:32b` | 📈 Optimization Master | Portfolio balancing, performance tracking |
| **Auditor** | `deepseek-r1:latest` | 🔍 Quality Controller | Performance review, compliance monitoring |

### Agent Communication Flow

```mermaid
graph TD
    MW[Market Watcher] --> CA[Chief Analyst]
    NA[News Analyst] --> CA
    TA[Technical Analyst] --> CA
    R[Researcher] --> CA
    CA --> RM[Risk Manager]
    CA --> T[Trader]
    RM --> T
    T --> PM[Portfolio Manager]
    PM --> A[Auditor]
    A --> CA
```

---

## 🚀 Quick Start

### Prerequisites

- **Python 3.9+**
- **Docker & Docker Compose**
- **Ollama** with required models
- **8GB+ RAM** (16GB recommended)
- **10GB+ free disk space**

### 1. Clone Repository

```bash
git clone https://github.com/yourusername/noryonv2.git
cd noryonv2
```

### 2. Run Automated Setup

```bash
python setup.py
```

The setup script will:
- ✅ Check system prerequisites
- 📁 Create project directories
- 🐍 Setup virtual environment
- 📦 Install dependencies
- 🗄️ Configure databases
- 🤖 Setup Ollama models
- ⚙️ Create configuration files

### 3. Configure Environment

Edit `.env` file with your API keys:

```bash
# Exchange API Keys
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET_KEY=your_binance_secret_key

COINBASE_API_KEY=your_coinbase_api_key
COINBASE_SECRET_KEY=your_coinbase_secret_key
COINBASE_PASSPHRASE=your_coinbase_passphrase

# Data Providers
COINGECKO_API_KEY=your_coingecko_api_key
NEWSAPI_KEY=your_newsapi_key

# Notifications (Optional)
DISCORD_WEBHOOK_URL=your_discord_webhook
TELEGRAM_BOT_TOKEN=your_telegram_token
```

### 4. Start Services

```bash
# Start infrastructure services
docker-compose up -d

# Start the trading system
python main.py
```

### 5. Access Dashboards

- **Main Dashboard**: http://localhost:8000/dashboard
- **API Documentation**: http://localhost:8000/docs
- **Grafana Monitoring**: http://localhost:3000 (admin/noryon123)
- **Prometheus Metrics**: http://localhost:9090

---

## 📊 System Architecture

### Infrastructure Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │      Redis      │    │   ClickHouse    │
│  (Transactions) │    │   (Caching)     │    │  (Analytics)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    MongoDB      │    │   Prometheus    │    │     Grafana     │
│    (Logs)       │    │   (Metrics)     │    │  (Dashboards)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Data Flow Architecture

```
Exchange APIs → Market Watcher → Technical Analyst → Chief Analyst
     ↓               ↓               ↓                    ↓
News APIs → News Analyst → Researcher → Risk Manager → Trader
     ↓               ↓               ↓                    ↓
Database ← Portfolio Manager ← Auditor ← Trade Execution
```

---

## 🛠️ Configuration

### Trading Configuration

```python
# Trading Settings
PAPER_TRADING = True  # Start with paper trading
INITIAL_BALANCE = 10000
MAX_PORTFOLIO_RISK = 0.02  # 2% max risk per trade
MAX_POSITION_SIZE = 0.1    # 10% max position size

# Risk Management
STOP_LOSS_PERCENTAGE = 0.05    # 5% stop loss
TAKE_PROFIT_PERCENTAGE = 0.15  # 15% take profit
MAX_DRAWDOWN = 0.1             # 10% max drawdown
```

### Supported Trading Pairs

```python
TRADING_PAIRS = [
    'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT',
    'SOL/USDT', 'DOT/USDT', 'AVAX/USDT', 'MATIC/USDT',
    'LINK/USDT', 'UNI/USDT', 'ATOM/USDT', 'XRP/USDT'
]
```

### Exchange Configuration

```python
EXCHANGES = {
    'binance': {
        'enabled': True,
        'testnet': True,  # Start with testnet
        'trading_fees': 0.001
    },
    'coinbase': {
        'enabled': True,
        'sandbox': True,
        'trading_fees': 0.005
    }
}
```

---

## 📈 Trading Strategies

### 1. Momentum Trading
- **Trigger**: Price breakouts with volume confirmation
- **Entry**: Above resistance with strong volume
- **Exit**: Profit target or momentum reversal
- **Risk**: 2% stop loss

### 2. Arbitrage Trading
- **Trigger**: Price differences between exchanges
- **Entry**: Simultaneous buy/sell on different exchanges
- **Exit**: Price convergence
- **Risk**: Execution and slippage risk

### 3. Grid Trading
- **Trigger**: Range-bound market conditions
- **Entry**: Multiple buy/sell orders in grid pattern
- **Exit**: Profit from price oscillations
- **Risk**: Trend breakout protection

### 4. Mean Reversion
- **Trigger**: Oversold/overbought conditions
- **Entry**: RSI extremes with support/resistance
- **Exit**: Return to mean price
- **Risk**: Trend continuation protection

### 5. News-Based Trading
- **Trigger**: Significant news events
- **Entry**: Sentiment analysis confirmation
- **Exit**: News impact absorption
- **Risk**: False signal protection

---

## 🛡️ Risk Management

### Multi-Layer Protection

1. **Position Sizing**
   - Maximum 2% risk per trade
   - Maximum 10% position size
   - Dynamic sizing based on volatility

2. **Stop Loss Management**
   - Automatic stop loss orders
   - Trailing stops for profitable trades
   - Emergency liquidation protocols

3. **Portfolio Limits**
   - Maximum 10% drawdown limit
   - Correlation-based position limits
   - Sector exposure limits

4. **Real-time Monitoring**
   - Continuous risk assessment
   - Automated position adjustments
   - Emergency shutdown procedures

---

## 📊 Monitoring & Analytics

### Grafana Dashboards

1. **Trading Overview**
   - Portfolio performance
   - Active positions
   - P&L tracking
   - Risk metrics

2. **Market Analysis**
   - Price movements
   - Volume analysis
   - Volatility tracking
   - Correlation matrices

3. **Agent Performance**
   - Signal accuracy
   - Processing times
   - Success rates
   - Error tracking

4. **System Health**
   - Database performance
   - API response times
   - Memory usage
   - Error rates

### Key Metrics

- **Sharpe Ratio**: Risk-adjusted returns
- **Maximum Drawdown**: Largest peak-to-trough decline
- **Win Rate**: Percentage of profitable trades
- **Profit Factor**: Gross profit / Gross loss
- **Average Trade**: Mean profit per trade

---

## 🔧 API Documentation

### REST API Endpoints

```bash
# System Status
GET /health                 # System health check
GET /status                 # Detailed system status

# Portfolio Management
GET /portfolio              # Portfolio overview
GET /portfolio/positions    # Current positions
GET /portfolio/performance  # Performance metrics

# Trading Operations
POST /orders                # Create new order
GET /orders                 # List orders
DELETE /orders/{id}         # Cancel order

# Market Data
GET /market/prices          # Current prices
GET /market/signals         # Trading signals
GET /market/analysis        # Market analysis

# Agent Management
GET /agents                 # Agent status
POST /agents/{name}/start   # Start agent
POST /agents/{name}/stop    # Stop agent
```

### WebSocket Streams

```javascript
// Real-time price updates
ws://localhost:8000/ws/prices

// Trading signals
ws://localhost:8000/ws/signals

// Portfolio updates
ws://localhost:8000/ws/portfolio

// System alerts
ws://localhost:8000/ws/alerts
```

---

## 🔒 Security

### Best Practices

1. **API Key Security**
   - Store keys in environment variables
   - Use read-only keys when possible
   - Rotate keys regularly
   - Monitor API usage

2. **Network Security**
   - Use HTTPS for all connections
   - Implement rate limiting
   - Monitor for suspicious activity
   - Use VPN for remote access

3. **Data Protection**
   - Encrypt sensitive data
   - Regular database backups
   - Access logging
   - Audit trails

4. **Operational Security**
   - Start with paper trading
   - Test thoroughly before live trading
   - Monitor system continuously
   - Have emergency procedures

---

## 📚 Advanced Usage

### Custom Strategy Development

```python
from src.strategies.base_strategy import BaseStrategy

class MyCustomStrategy(BaseStrategy):
    def __init__(self):
        super().__init__("my_custom_strategy")
    
    async def analyze(self, market_data):
        # Your analysis logic
        return signals
    
    async def generate_signals(self, analysis):
        # Your signal generation logic
        return trading_signals
```

### Custom Agent Development

```python
from src.agents.base_agent import BaseAgent

class MyCustomAgent(BaseAgent):
    def __init__(self, model_name, config, db_manager):
        super().__init__("my_custom_agent", model_name, config, db_manager)
    
    async def _perform_analysis(self, data):
        # Your custom analysis logic
        return results
```

### Backtesting

```python
from src.backtesting.engine import BacktestEngine

# Run backtest
engine = BacktestEngine()
results = await engine.run_backtest(
    strategy="momentum",
    start_date="2024-01-01",
    end_date="2024-12-31",
    initial_balance=10000
)

print(f"Total Return: {results.total_return:.2%}")
print(f"Sharpe Ratio: {results.sharpe_ratio:.2f}")
print(f"Max Drawdown: {results.max_drawdown:.2%}")
```

---

## 🐛 Troubleshooting

### Common Issues

1. **Ollama Connection Failed**
   ```bash
   # Check if Ollama is running
   ollama list
   
   # Start Ollama service
   ollama serve
   
   # Pull required models
   ollama pull magistral:24b
   ```

2. **Database Connection Error**
   ```bash
   # Check Docker services
   docker-compose ps
   
   # Restart services
   docker-compose restart postgres redis
   
   # Check logs
   docker-compose logs postgres
   ```

3. **Exchange API Errors**
   ```bash
   # Verify API keys in .env
   # Check exchange status
   # Verify network connectivity
   # Check rate limits
   ```

4. **High Memory Usage**
   ```bash
   # Monitor system resources
   htop
   
   # Adjust model parameters
   # Reduce concurrent agents
   # Increase swap space
   ```

### Log Analysis

```bash
# View system logs
tail -f logs/noryon.log

# View trading logs
tail -f logs/trading.log

# View agent logs
tail -f logs/agents.log

# View error logs
tail -f logs/errors.log
```

---

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

```bash
# Clone repository
git clone https://github.com/yourusername/noryonv2.git
cd noryonv2

# Install development dependencies
pip install -r requirements-dev.txt

# Run tests
pytest

# Code formatting
black src/
flake8 src/

# Type checking
mypy src/
```

---

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## ⚠️ Disclaimer

**IMPORTANT**: This software is for educational and research purposes only. Cryptocurrency trading involves substantial risk of loss and is not suitable for all investors. The authors and contributors are not responsible for any financial losses incurred through the use of this software.

### Risk Warnings

- **High Risk**: Cryptocurrency trading is highly speculative and risky
- **No Guarantees**: Past performance does not guarantee future results
- **Start Small**: Begin with paper trading and small amounts
- **Do Your Research**: Understand the markets before trading
- **Seek Advice**: Consult with financial advisors if needed

---

## 📞 Support

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/yourusername/noryonv2/issues)
- **Discussions**: [GitHub Discussions](https://github.com/yourusername/noryonv2/discussions)
- **Discord**: [Join our Discord](https://discord.gg/noryonv2)

---

## 🙏 Acknowledgments

- **Ollama Team** - For the amazing local AI model platform
- **FastAPI** - For the excellent web framework
- **CCXT** - For unified exchange APIs
- **Open Source Community** - For the incredible tools and libraries

---

<div align="center">

**Built with ❤️ by the Noryon Team**

[![GitHub Stars](https://img.shields.io/github/stars/yourusername/noryonv2?style=social)](https://github.com/yourusername/noryonv2)
[![GitHub Forks](https://img.shields.io/github/forks/yourusername/noryonv2?style=social)](https://github.com/yourusername/noryonv2)

</div>