"""
Real Binance Exchange Data Connector
Connects to live Binance WebSocket feeds for real market data
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Callable
import aiohttp
import websockets
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

class BinanceConnector:
    """Real-time Binance data connector"""
    
    def __init__(self):
        self.base_url = "https://api.binance.com"
        self.ws_url = "wss://stream.binance.com:9443/ws"
        self.symbols = [
            "BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "SOLUSDT",
            "DOTUSDT", "MATICUSDT", "AVAXUSDT", "LINKUSDT", "UNIUSDT"
        ]
        self.running = False
        self.subscribers = []
        self.latest_ticks = {}
        self.websocket = None
        
    def add_subscriber(self, callback: Callable):
        """Add a callback function to receive market data"""
        self.subscribers.append(callback)
        
    def remove_subscriber(self, callback: Callable):
        """Remove a subscriber"""
        if callback in self.subscribers:
            self.subscribers.remove(callback)
    
    async def start(self):
        """Start the real-time data connection"""
        self.running = True
        logger.info("🚀 Starting Binance real-time data connector")
        
        try:
            # Get initial 24hr ticker data
            await self._get_initial_data()
            
            # Start WebSocket connection
            await self._start_websocket()
            
        except Exception as e:
            logger.error(f"Failed to start Binance connector: {e}")
            raise
    
    def stop(self):
        """Stop the data connection"""
        self.running = False
        logger.info("⏹️ Stopping Binance data connector")
    
    async def _get_initial_data(self):
        """Get initial 24hr ticker data for all symbols"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/api/v3/ticker/24hr") as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        for ticker in data:
                            symbol = ticker["symbol"]
                            if symbol in self.symbols:
                                tick = self._format_ticker_data(ticker)
                                self.latest_ticks[symbol] = tick
                                
                                # Notify subscribers
                                await self._notify_subscribers(tick)
                        
                        logger.info(f"✅ Loaded initial data for {len(self.latest_ticks)} symbols")
                    else:
                        logger.error(f"Failed to get initial data: HTTP {response.status}")
                        
        except Exception as e:
            logger.error(f"Error getting initial data: {e}")
    
    async def _start_websocket(self):
        """Start WebSocket connection for real-time data"""
        # Create stream names for all symbols
        streams = [f"{symbol.lower()}@ticker" for symbol in self.symbols]
        stream_url = f"{self.ws_url}/{'/'.join(streams)}"
        
        logger.info(f"🔌 Connecting to Binance WebSocket: {len(streams)} streams")
        
        try:
            async with websockets.connect(stream_url) as websocket:
                self.websocket = websocket
                logger.info("✅ Connected to Binance WebSocket")
                
                while self.running:
                    try:
                        message = await websocket.recv()
                        data = json.loads(message)
                        
                        # Handle ticker data
                        if "stream" in data and "data" in data:
                            await self._handle_ticker_update(data["data"])
                        
                    except websockets.exceptions.ConnectionClosed:
                        logger.warning("WebSocket connection closed")
                        break
                    except Exception as e:
                        logger.error(f"WebSocket error: {e}")
                        await asyncio.sleep(1)
                        
        except Exception as e:
            logger.error(f"WebSocket connection failed: {e}")
            # Fallback to polling mode
            await self._polling_fallback()
    
    async def _handle_ticker_update(self, data: Dict[str, Any]):
        """Handle real-time ticker updates"""
        try:
            symbol = data["s"]
            if symbol in self.symbols:
                tick = self._format_websocket_data(data)
                self.latest_ticks[symbol] = tick
                
                # Notify subscribers
                await self._notify_subscribers(tick)
                
        except Exception as e:
            logger.error(f"Error handling ticker update: {e}")
    
    def _format_ticker_data(self, ticker: Dict[str, Any]) -> Dict[str, Any]:
        """Format 24hr ticker data"""
        return {
            "symbol": ticker["symbol"],
            "price": float(ticker["lastPrice"]),
            "volume": float(ticker["volume"]),
            "change_24h": float(ticker["priceChangePercent"]),
            "high_24h": float(ticker["highPrice"]),
            "low_24h": float(ticker["lowPrice"]),
            "bid": float(ticker["bidPrice"]),
            "ask": float(ticker["askPrice"]),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "source": "binance_api"
        }
    
    def _format_websocket_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Format WebSocket ticker data"""
        return {
            "symbol": data["s"],
            "price": float(data["c"]),  # Close price
            "volume": float(data["v"]),  # Volume
            "change_24h": float(data["P"]),  # Price change percent
            "high_24h": float(data["h"]),  # High price
            "low_24h": float(data["l"]),  # Low price
            "bid": float(data["b"]),  # Best bid price
            "ask": float(data["a"]),  # Best ask price
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "source": "binance_ws"
        }
    
    async def _notify_subscribers(self, tick: Dict[str, Any]):
        """Notify all subscribers of new tick data"""
        for callback in self.subscribers:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(tick)
                else:
                    callback(tick)
            except Exception as e:
                logger.error(f"Error notifying subscriber: {e}")
    
    async def _polling_fallback(self):
        """Fallback to polling mode if WebSocket fails"""
        logger.info("🔄 Falling back to polling mode")
        
        while self.running:
            try:
                await self._get_initial_data()
                await asyncio.sleep(5)  # Poll every 5 seconds
                
            except Exception as e:
                logger.error(f"Polling error: {e}")
                await asyncio.sleep(10)
    
    def get_latest_tick(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get latest tick for a symbol"""
        return self.latest_ticks.get(symbol)
    
    def get_all_latest(self) -> Dict[str, Dict[str, Any]]:
        """Get all latest ticks"""
        return self.latest_ticks.copy()
    
    async def get_klines(self, symbol: str, interval: str = "1h", limit: int = 100) -> List[Dict[str, Any]]:
        """Get historical kline/candlestick data"""
        try:
            params = {
                "symbol": symbol,
                "interval": interval,
                "limit": limit
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/api/v3/klines", params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        klines = []
                        for kline in data:
                            klines.append({
                                "timestamp": datetime.fromtimestamp(kline[0] / 1000, timezone.utc).isoformat(),
                                "open": float(kline[1]),
                                "high": float(kline[2]),
                                "low": float(kline[3]),
                                "close": float(kline[4]),
                                "volume": float(kline[5])
                            })
                        
                        return klines
                    else:
                        logger.error(f"Failed to get klines: HTTP {response.status}")
                        return []
                        
        except Exception as e:
            logger.error(f"Error getting klines: {e}")
            return []

# Global Binance connector instance
binance_connector = BinanceConnector()

async def start_binance_data():
    """Start the Binance data system"""
    logger.info("🚀 Starting Binance Data System...")
    await binance_connector.start()

def stop_binance_data():
    """Stop the Binance data system"""
    logger.info("⏹️ Stopping Binance Data System...")
    binance_connector.stop()

if __name__ == "__main__":
    # Test the Binance connector
    async def test_connector():
        def print_tick(tick):
            print(f"📊 {tick['symbol']}: ${tick['price']:,.2f} ({tick['change_24h']:+.2f}%)")
        
        binance_connector.add_subscriber(print_tick)
        
        print("🚀 Starting Binance connector test...")
        try:
            await binance_connector.start()
            await asyncio.sleep(30)  # Run for 30 seconds
        except KeyboardInterrupt:
            print("⏹️ Test stopped")
        finally:
            binance_connector.stop()
    
    asyncio.run(test_connector())
