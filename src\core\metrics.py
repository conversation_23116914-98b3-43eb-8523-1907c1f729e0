"""Central Prometheus metrics registry for the Noryon project.

This module should be imported **before** any code that registers or
uses these metrics to avoid duplicate-registration issues.
"""

from __future__ import annotations

from prometheus_client import Counter, Gauge, Histogram

# ---------------------------------------------------------------------------
# HTTP layer metrics
# ---------------------------------------------------------------------------
REQUEST_LATENCY = Histogram(
    "http_request_latency_seconds",
    "Latency of HTTP requests in seconds",
    ["method", "path"],
)

REQUEST_COUNT = Counter(
    "http_requests_total",
    "Total count of HTTP requests",
    ["method", "path", "status_code"],
)

# ---------------------------------------------------------------------------
# Application / agent metrics
# ---------------------------------------------------------------------------
AGENT_HEARTBEATS = Counter(
    "agent_heartbeats_total",
    "Total heartbeat messages received from all agents",
)

APP_READY = Gauge("app_ready", "Whether the FastAPI app is ready to serve traffic")

# ---------------------------------------------------------------------------
# Market data metrics (Sprint-1)
# ---------------------------------------------------------------------------
TICKS_INGESTED = Counter(
    "ticks_ingested_total",
    "Total number of price ticks ingested",
    ["symbol", "exchange"],
)

TICK_LATENCY = Histogram(
    "tick_latency_seconds",
    "Latency from exchange timestamp to ingest",
    ["symbol"],
    buckets=(0.01, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0),
)

STRATEGY_SIGNALS = Counter(
    "strategy_signals_total",
    "Total trading signals generated",
    ["strategy", "symbol", "side"],
)

MARKET_DATA_ERRORS = Counter(
    "market_data_errors_total",
    "Total errors in market data ingestion",
    ["exchange", "error_type"],
) 