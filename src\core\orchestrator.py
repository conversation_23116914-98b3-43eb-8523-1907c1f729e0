"""
Noryon V2 - System Orchestrator
Central coordination system for all AI agents and trading operations
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json
from decimal import Decimal

from src.core.config import Config
from src.core.logger import get_logger, get_performance_logger, get_trading_logger
from src.db.database_manager import DatabaseManager
from src.exchanges.exchange_manager import ExchangeManager

class AgentStatus(Enum):
    INITIALIZING = "initializing"
    ACTIVE = "active"
    IDLE = "idle"
    ERROR = "error"
    STOPPED = "stopped"

class TradingMode(Enum):
    PAPER = "paper"
    LIVE = "live"
    SIMULATION = "simulation"

@dataclass
class AgentMessage:
    """Message structure for inter-agent communication"""
    source_agent: str
    target_agent: str
    message_type: str
    content: Dict[str, Any]
    timestamp: datetime
    priority: int = 1  # 1=low, 2=medium, 3=high, 4=critical
    requires_response: bool = False
    correlation_id: Optional[str] = None

@dataclass
class TradingSignal:
    """Trading signal structure"""
    symbol: str
    action: str  # buy, sell, hold
    strength: float  # 0.0 to 1.0
    price_target: Optional[float]
    stop_loss: Optional[float]
    take_profit: Optional[float]
    strategy: str
    agent_source: str
    timestamp: datetime
    confidence: float
    reasoning: str
    risk_score: float

@dataclass
class MarketData:
    """Market data structure"""
    symbol: str
    price: float
    volume: float
    timestamp: datetime
    bid: Optional[float] = None
    ask: Optional[float] = None
    high_24h: Optional[float] = None
    low_24h: Optional[float] = None
    change_24h: Optional[float] = None

class SystemOrchestrator:
    """Central orchestrator for the entire trading system"""
    
    def __init__(self, agents: Dict[str, Any], db_manager: DatabaseManager, 
                 exchange_manager: ExchangeManager, config: Config):
        self.agents = agents
        self.db_manager = db_manager
        self.exchange_manager = exchange_manager
        self.config = config
        
        self.logger = get_logger(__name__)
        self.performance_logger = get_performance_logger("orchestrator")
        self.trading_logger = get_trading_logger("orchestrator")
        
        self.running = False
        self.trading_mode = TradingMode.PAPER if config.PAPER_TRADING else TradingMode.LIVE
        
        # Communication system
        self.message_queue: asyncio.Queue = asyncio.Queue()
        self.agent_status: Dict[str, AgentStatus] = {}
        self.agent_last_heartbeat: Dict[str, datetime] = {}
        
        # Trading state
        self.active_signals: Dict[str, List[TradingSignal]] = {}
        self.pending_orders: Dict[str, Any] = {}
        self.portfolio_state: Dict[str, Any] = {}
        self.risk_metrics: Dict[str, Any] = {}
        
        # Market data cache
        self.market_data_cache: Dict[str, MarketData] = {}
        self.last_market_update: Dict[str, datetime] = {}
        
        # Performance tracking
        self.system_metrics: Dict[str, Any] = {
            "trades_executed": 0,
            "signals_generated": 0,
            "decisions_made": 0,
            "errors_encountered": 0,
            "uptime_start": datetime.utcnow()
        }
        
        # Task management
        self.background_tasks: List[asyncio.Task] = []
        
    async def initialize(self):
        """Initialize the orchestrator"""
        try:
            self.logger.info("🎯 Initializing System Orchestrator...")
            
            # Initialize agent status tracking
            for agent_name in self.agents.keys():
                self.agent_status[agent_name] = AgentStatus.INITIALIZING
                self.agent_last_heartbeat[agent_name] = datetime.utcnow()
                self.active_signals[agent_name] = []
            
            # Initialize portfolio state
            await self._initialize_portfolio_state()
            
            # Initialize risk metrics
            await self._initialize_risk_metrics()
            
            self.logger.info("✅ System Orchestrator initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize orchestrator: {e}")
            raise
    
    async def start(self):
        """Start the orchestrator and all background tasks"""
        try:
            self.running = True
            self.logger.info("🚀 Starting System Orchestrator...")
            
            # Start background tasks
            self.background_tasks = [
                asyncio.create_task(self._message_processor()),
                asyncio.create_task(self._agent_health_monitor()),
                asyncio.create_task(self._market_data_processor()),
                asyncio.create_task(self._trading_decision_engine()),
                asyncio.create_task(self._risk_monitor()),
                asyncio.create_task(self._performance_tracker()),
                asyncio.create_task(self._portfolio_manager()),
                asyncio.create_task(self._system_health_monitor())
            ]
            
            # Mark all agents as active
            for agent_name in self.agents.keys():
                self.agent_status[agent_name] = AgentStatus.ACTIVE
            
            self.logger.info("✅ System Orchestrator started successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start orchestrator: {e}")
            raise
    
    async def stop(self):
        """Stop the orchestrator and all background tasks"""
        self.logger.info("🛑 Stopping System Orchestrator...")
        self.running = False
        
        # Cancel all background tasks
        for task in self.background_tasks:
            task.cancel()
        
        # Wait for tasks to complete
        if self.background_tasks:
            await asyncio.gather(*self.background_tasks, return_exceptions=True)
        
        # Mark all agents as stopped
        for agent_name in self.agents.keys():
            self.agent_status[agent_name] = AgentStatus.STOPPED
        
        self.logger.info("✅ System Orchestrator stopped")
    
    async def send_message(self, message: AgentMessage):
        """Send a message between agents"""
        await self.message_queue.put(message)
        
        self.logger.debug(
            f"Message queued: {message.source_agent} -> {message.target_agent} ({message.message_type})",
            extra={
                "source": message.source_agent,
                "target": message.target_agent,
                "type": message.message_type,
                "priority": message.priority
            }
        )
    
    async def submit_trading_signal(self, signal: TradingSignal):
        """Submit a trading signal for processing"""
        self.active_signals[signal.agent_source].append(signal)
        self.system_metrics["signals_generated"] += 1
        
        self.trading_logger.log_signal(
            signal_type=signal.action,
            symbol=signal.symbol,
            strength=signal.strength,
            source=signal.agent_source,
            confidence=signal.confidence,
            strategy=signal.strategy,
            reasoning=signal.reasoning
        )
        
        # Notify relevant agents
        await self.send_message(AgentMessage(
            source_agent="orchestrator",
            target_agent="risk_manager",
            message_type="signal_evaluation",
            content=signal.__dict__,
            timestamp=datetime.utcnow(),
            priority=3
        ))
    
    async def get_market_data(self, symbol: str) -> Optional[MarketData]:
        """Get cached market data for a symbol"""
        return self.market_data_cache.get(symbol)
    
    async def get_portfolio_state(self) -> Dict[str, Any]:
        """Get current portfolio state"""
        return self.portfolio_state.copy()
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        uptime = datetime.utcnow() - self.system_metrics["uptime_start"]
        
        return {
            "status": "running" if self.running else "stopped",
            "trading_mode": self.trading_mode.value,
            "uptime_seconds": uptime.total_seconds(),
            "agents": {
                name: {
                    "status": status.value,
                    "last_heartbeat": self.agent_last_heartbeat.get(name),
                    "active_signals": len(self.active_signals.get(name, []))
                }
                for name, status in self.agent_status.items()
            },
            "metrics": self.system_metrics,
            "portfolio": self.portfolio_state,
            "risk_metrics": self.risk_metrics,
            "market_data_symbols": len(self.market_data_cache),
            "pending_orders": len(self.pending_orders)
        }
    
    # Background Task Methods
    
    async def _message_processor(self):
        """Process inter-agent messages"""
        while self.running:
            try:
                # Get message with timeout
                message = await asyncio.wait_for(
                    self.message_queue.get(), 
                    timeout=1.0
                )
                
                await self._handle_message(message)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error processing message: {e}")
                self.system_metrics["errors_encountered"] += 1
    
    async def _handle_message(self, message: AgentMessage):
        """Handle a specific message"""
        try:
            target_agent = self.agents.get(message.target_agent)
            if not target_agent:
                self.logger.warning(f"Target agent not found: {message.target_agent}")
                return
            
            # Route message based on type
            if message.message_type == "market_update":
                await self._handle_market_update(message)
            elif message.message_type == "trading_signal":
                await self._handle_trading_signal(message)
            elif message.message_type == "risk_alert":
                await self._handle_risk_alert(message)
            elif message.message_type == "portfolio_update":
                await self._handle_portfolio_update(message)
            else:
                # Forward to target agent
                if hasattr(target_agent, 'receive_message'):
                    await target_agent.receive_message(message)
            
        except Exception as e:
            self.logger.error(f"Error handling message: {e}")
    
    async def _agent_health_monitor(self):
        """Monitor agent health and heartbeats"""
        while self.running:
            try:
                current_time = datetime.utcnow()
                
                for agent_name, last_heartbeat in self.agent_last_heartbeat.items():
                    time_since_heartbeat = current_time - last_heartbeat
                    
                    if time_since_heartbeat > timedelta(minutes=5):
                        if self.agent_status[agent_name] != AgentStatus.ERROR:
                            self.logger.warning(f"Agent {agent_name} appears unresponsive")
                            self.agent_status[agent_name] = AgentStatus.ERROR
                    elif self.agent_status[agent_name] == AgentStatus.ERROR:
                        self.logger.info(f"Agent {agent_name} recovered")
                        self.agent_status[agent_name] = AgentStatus.ACTIVE
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in agent health monitor: {e}")
                await asyncio.sleep(30)
    
    async def _market_data_processor(self):
        """Process and cache market data"""
        while self.running:
            try:
                # Get market data from exchanges
                for symbol in self.config.TRADING_PAIRS:
                    try:
                        market_data = await self.exchange_manager.get_ticker(symbol)
                        if market_data:
                            self.market_data_cache[symbol] = MarketData(
                                symbol=symbol,
                                price=market_data.get('last', 0),
                                volume=market_data.get('volume', 0),
                                timestamp=datetime.utcnow(),
                                bid=market_data.get('bid'),
                                ask=market_data.get('ask'),
                                high_24h=market_data.get('high'),
                                low_24h=market_data.get('low'),
                                change_24h=market_data.get('change')
                            )
                            self.last_market_update[symbol] = datetime.utcnow()
                    except Exception as e:
                        self.logger.error(f"Error getting market data for {symbol}: {e}")
                
                await asyncio.sleep(5)  # Update every 5 seconds
                
            except Exception as e:
                self.logger.error(f"Error in market data processor: {e}")
                await asyncio.sleep(5)
    
    async def _trading_decision_engine(self):
        """Main trading decision engine"""
        while self.running:
            try:
                # Collect signals from all agents
                all_signals = []
                for agent_signals in self.active_signals.values():
                    all_signals.extend(agent_signals)
                
                if not all_signals:
                    await asyncio.sleep(10)
                    continue
                
                # Group signals by symbol
                signals_by_symbol = {}
                for signal in all_signals:
                    if signal.symbol not in signals_by_symbol:
                        signals_by_symbol[signal.symbol] = []
                    signals_by_symbol[signal.symbol].append(signal)
                
                # Process each symbol
                for symbol, signals in signals_by_symbol.items():
                    await self._process_symbol_signals(symbol, signals)
                
                # Clear processed signals
                for agent_name in self.active_signals:
                    self.active_signals[agent_name] = []
                
                await asyncio.sleep(10)  # Process every 10 seconds
                
            except Exception as e:
                self.logger.error(f"Error in trading decision engine: {e}")
                await asyncio.sleep(10)
    
    async def _process_symbol_signals(self, symbol: str, signals: List[TradingSignal]):
        """Process trading signals for a specific symbol"""
        try:
            if not signals:
                return
            
            # Calculate consensus
            buy_signals = [s for s in signals if s.action == 'buy']
            sell_signals = [s for s in signals if s.action == 'sell']
            hold_signals = [s for s in signals if s.action == 'hold']
            
            # Weight signals by confidence and agent reliability
            buy_weight = sum(s.strength * s.confidence for s in buy_signals)
            sell_weight = sum(s.strength * s.confidence for s in sell_signals)
            
            # Make trading decision
            if buy_weight > sell_weight and buy_weight > 0.6:
                await self._execute_buy_decision(symbol, buy_signals)
            elif sell_weight > buy_weight and sell_weight > 0.6:
                await self._execute_sell_decision(symbol, sell_signals)
            
            self.system_metrics["decisions_made"] += 1
            
        except Exception as e:
            self.logger.error(f"Error processing signals for {symbol}: {e}")
    
    async def _execute_buy_decision(self, symbol: str, signals: List[TradingSignal]):
        """Execute a buy decision"""
        try:
            # Calculate position size based on risk management
            position_size = await self._calculate_position_size(symbol, 'buy', signals)
            
            if position_size <= 0:
                return
            
            # Get current market price
            market_data = self.market_data_cache.get(symbol)
            if not market_data:
                return
            
            # Calculate stop loss and take profit
            avg_stop_loss = sum(s.stop_loss for s in signals if s.stop_loss) / len([s for s in signals if s.stop_loss])
            avg_take_profit = sum(s.take_profit for s in signals if s.take_profit) / len([s for s in signals if s.take_profit])
            
            # Execute trade
            if self.trading_mode == TradingMode.PAPER:
                await self._execute_paper_trade(symbol, 'buy', position_size, market_data.price)
            else:
                await self._execute_live_trade(symbol, 'buy', position_size, market_data.price)
            
            self.trading_logger.log_trade(
                action='buy',
                symbol=symbol,
                quantity=position_size,
                price=market_data.price,
                exchange='aggregated',
                strategy='consensus',
                signals_count=len(signals),
                stop_loss=avg_stop_loss,
                take_profit=avg_take_profit
            )
            
        except Exception as e:
            self.logger.error(f"Error executing buy decision for {symbol}: {e}")
    
    async def _execute_sell_decision(self, symbol: str, signals: List[TradingSignal]):
        """Execute a sell decision"""
        try:
            # Check current position
            current_position = self.portfolio_state.get('positions', {}).get(symbol, 0)
            if current_position <= 0:
                return
            
            # Calculate sell quantity
            sell_quantity = min(current_position, await self._calculate_sell_quantity(symbol, signals))
            
            if sell_quantity <= 0:
                return
            
            # Get current market price
            market_data = self.market_data_cache.get(symbol)
            if not market_data:
                return
            
            # Execute trade
            if self.trading_mode == TradingMode.PAPER:
                await self._execute_paper_trade(symbol, 'sell', sell_quantity, market_data.price)
            else:
                await self._execute_live_trade(symbol, 'sell', sell_quantity, market_data.price)
            
            self.trading_logger.log_trade(
                action='sell',
                symbol=symbol,
                quantity=sell_quantity,
                price=market_data.price,
                exchange='aggregated',
                strategy='consensus',
                signals_count=len(signals)
            )
            
        except Exception as e:
            self.logger.error(f"Error executing sell decision for {symbol}: {e}")
    
    async def _risk_monitor(self):
        """Monitor risk metrics and portfolio health"""
        while self.running:
            try:
                await self._update_risk_metrics()
                await self._check_risk_limits()
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error(f"Error in risk monitor: {e}")
                await asyncio.sleep(60)
    
    async def _performance_tracker(self):
        """Track system performance metrics"""
        while self.running:
            try:
                await self._update_performance_metrics()
                await asyncio.sleep(300)  # Update every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Error in performance tracker: {e}")
                await asyncio.sleep(300)
    
    async def _portfolio_manager(self):
        """Manage portfolio state and rebalancing"""
        while self.running:
            try:
                await self._update_portfolio_state()
                await self._check_rebalancing_needs()
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Error in portfolio manager: {e}")
                await asyncio.sleep(300)
    
    async def _system_health_monitor(self):
        """Monitor overall system health"""
        while self.running:
            try:
                await self._check_system_health()
                await asyncio.sleep(120)  # Check every 2 minutes
                
            except Exception as e:
                self.logger.error(f"Error in system health monitor: {e}")
                await asyncio.sleep(120)
    
    # Helper Methods
    
    async def _initialize_portfolio_state(self):
        """Initialize portfolio state"""
        self.portfolio_state = {
            "total_value": float(self.config.INITIAL_BALANCE),
            "cash_balance": float(self.config.INITIAL_BALANCE),
            "positions": {},
            "pnl": 0.0,
            "daily_pnl": 0.0,
            "total_trades": 0,
            "winning_trades": 0,
            "losing_trades": 0
        }
    
    async def _initialize_risk_metrics(self):
        """Initialize risk metrics"""
        self.risk_metrics = {
            "portfolio_risk": 0.0,
            "var_95": 0.0,
            "max_drawdown": 0.0,
            "sharpe_ratio": 0.0,
            "beta": 0.0,
            "correlation_matrix": {},
            "risk_alerts": []
        }
    
    async def _calculate_position_size(self, symbol: str, action: str, signals: List[TradingSignal]) -> float:
        """Calculate appropriate position size"""
        # Implement position sizing logic based on risk management
        max_position_value = self.portfolio_state["total_value"] * float(self.config.MAX_POSITION_SIZE)
        
        # Get current price
        market_data = self.market_data_cache.get(symbol)
        if not market_data:
            return 0.0
        
        # Calculate position size
        position_size = max_position_value / market_data.price
        
        # Apply risk adjustments
        avg_risk_score = sum(s.risk_score for s in signals) / len(signals)
        position_size *= (1.0 - avg_risk_score)
        
        return max(0.0, position_size)
    
    async def _calculate_sell_quantity(self, symbol: str, signals: List[TradingSignal]) -> float:
        """Calculate sell quantity based on signals"""
        current_position = self.portfolio_state.get('positions', {}).get(symbol, 0)
        
        # Calculate sell percentage based on signal strength
        avg_strength = sum(s.strength for s in signals) / len(signals)
        sell_percentage = min(1.0, avg_strength)
        
        return current_position * sell_percentage
    
    async def _execute_paper_trade(self, symbol: str, action: str, quantity: float, price: float):
        """Execute a paper trade"""
        trade_value = quantity * price
        
        if action == 'buy':
            if self.portfolio_state["cash_balance"] >= trade_value:
                self.portfolio_state["cash_balance"] -= trade_value
                current_position = self.portfolio_state["positions"].get(symbol, 0)
                self.portfolio_state["positions"][symbol] = current_position + quantity
        elif action == 'sell':
            current_position = self.portfolio_state["positions"].get(symbol, 0)
            if current_position >= quantity:
                self.portfolio_state["positions"][symbol] = current_position - quantity
                self.portfolio_state["cash_balance"] += trade_value
        
        self.portfolio_state["total_trades"] += 1
        self.system_metrics["trades_executed"] += 1
    
    async def _execute_live_trade(self, symbol: str, action: str, quantity: float, price: float):
        """Execute a live trade"""
        # Implement live trading logic
        # This would interface with the exchange manager
        pass
    
    async def _update_risk_metrics(self):
        """Update risk metrics"""
        # Implement risk calculation logic
        pass
    
    async def _check_risk_limits(self):
        """Check if risk limits are exceeded"""
        # Implement risk limit checking
        pass
    
    async def _update_performance_metrics(self):
        """Update performance metrics"""
        # Implement performance calculation logic
        pass
    
    async def _update_portfolio_state(self):
        """Update portfolio state"""
        # Calculate current portfolio value
        total_value = self.portfolio_state["cash_balance"]
        
        for symbol, quantity in self.portfolio_state["positions"].items():
            market_data = self.market_data_cache.get(symbol)
            if market_data:
                total_value += quantity * market_data.price
        
        self.portfolio_state["total_value"] = total_value
        
        # Calculate PnL
        initial_balance = float(self.config.INITIAL_BALANCE)
        self.portfolio_state["pnl"] = total_value - initial_balance
    
    async def _check_rebalancing_needs(self):
        """Check if portfolio needs rebalancing"""
        # Implement rebalancing logic
        pass
    
    async def _check_system_health(self):
        """Check overall system health"""
        # Implement system health checks
        pass
    
    async def _handle_market_update(self, message: AgentMessage):
        """Handle market update message"""
        pass
    
    async def _handle_trading_signal(self, message: AgentMessage):
        """Handle trading signal message"""
        pass
    
    async def _handle_risk_alert(self, message: AgentMessage):
        """Handle risk alert message"""
        pass
    
    async def _handle_portfolio_update(self, message: AgentMessage):
        """Handle portfolio update message"""
        pass 