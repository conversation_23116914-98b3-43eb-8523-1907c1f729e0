"""
AI Agent Manager for Noryon V2
Coordinates and manages all AI trading agents
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

from src.agents.base import AbstractAgent
from src.agents.strategy_researcher import StrategyResearcher
from src.services.ai_service import ai_service
from src.services.market_simulator import market_broadcaster
from src.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

class MarketWatcherAgent(AbstractAgent):
    """AI-powered market monitoring agent"""
    NAME = "market_watcher"
    
    def __init__(self):
        super().__init__()
        self.analysis_interval = 30  # seconds
        
    async def start(self):
        await super().start()
        # Start market monitoring
        asyncio.create_task(self._monitor_market())
        
    async def _monitor_market(self):
        """Monitor market conditions and provide AI insights"""
        while self._running:
            try:
                # Get latest market data
                latest_ticks = market_broadcaster.get_all_latest()
                
                for symbol, tick in latest_ticks.items():
                    if abs(tick.get("change_24h", 0)) > 1.0:  # Significant movement
                        await self._analyze_movement(symbol, tick)
                
                await asyncio.sleep(self.analysis_interval)
                
            except Exception as e:
                logger.error(f"Market monitoring error: {e}")
                await asyncio.sleep(5)
    
    async def _analyze_movement(self, symbol: str, tick: Dict[str, Any]):
        """Analyze significant market movements with AI"""
        try:
            analysis = await ai_service.analyze_market_data(symbol, tick)
            
            logger.info(f"🔍 Market Watcher AI: {symbol} - {analysis[:150]}...")
            
            # Store analysis (would normally go to Redis/DB)
            analysis_data = {
                "agent": self.NAME,
                "symbol": symbol,
                "analysis": analysis,
                "tick_data": tick,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Publish to other agents
            await self._broadcast_analysis(analysis_data)
            
        except Exception as e:
            logger.error(f"Analysis error for {symbol}: {e}")
    
    async def _broadcast_analysis(self, analysis: Dict[str, Any]):
        """Broadcast analysis to other agents"""
        # Would normally publish to Redis pubsub
        logger.debug(f"Broadcasting analysis: {analysis['symbol']}")

class RiskOfficerAgent(AbstractAgent):
    """AI-powered risk management agent"""
    NAME = "risk_officer"
    
    def __init__(self):
        super().__init__()
        self.risk_check_interval = 60  # seconds
        self.portfolio = {
            "total_value": 10000.0,  # Starting with $10k
            "positions": {},
            "max_risk_per_trade": 0.02,  # 2%
            "max_portfolio_risk": 0.1   # 10%
        }
        
    async def start(self):
        await super().start()
        asyncio.create_task(self._monitor_risk())
        
    async def _monitor_risk(self):
        """Monitor portfolio risk continuously"""
        while self._running:
            try:
                await self._assess_portfolio_risk()
                await asyncio.sleep(self.risk_check_interval)
                
            except Exception as e:
                logger.error(f"Risk monitoring error: {e}")
                await asyncio.sleep(10)
    
    async def _assess_portfolio_risk(self):
        """Assess current portfolio risk with AI"""
        try:
            risk_assessment = await ai_service.assess_risk(
                self.portfolio, 
                {"current_positions": len(self.portfolio["positions"])}
            )
            
            logger.info(f"Risk Officer AI: {risk_assessment[:100]}...")
            
        except Exception as e:
            logger.error(f"Risk assessment error: {e}")

class TechnicalAnalystAgent(AbstractAgent):
    """AI-powered technical analysis agent"""
    NAME = "technical_analyst"
    
    def __init__(self):
        super().__init__()
        self.analysis_interval = 45  # seconds
        
    async def start(self):
        await super().start()
        asyncio.create_task(self._perform_technical_analysis())
        
    async def _perform_technical_analysis(self):
        """Perform technical analysis on market data"""
        while self._running:
            try:
                latest_ticks = market_broadcaster.get_all_latest()
                
                for symbol, tick in latest_ticks.items():
                    await self._analyze_technicals(symbol, tick)
                
                await asyncio.sleep(self.analysis_interval)
                
            except Exception as e:
                logger.error(f"Technical analysis error: {e}")
                await asyncio.sleep(10)
    
    async def _analyze_technicals(self, symbol: str, tick: Dict[str, Any]):
        """Analyze technical indicators with AI"""
        try:
            # Mock technical indicators
            indicators = {
                "rsi": 45.5,
                "macd": 0.12,
                "ma_20": tick.get("price", 0) * 0.98,
                "ma_50": tick.get("price", 0) * 0.95,
                "volume_avg": tick.get("volume", 0) * 1.2
            }
            
            analysis = await ai_service.technical_analysis(symbol, indicators)
            
            logger.info(f"📊 Technical Analyst AI: {symbol} - {analysis[:100]}...")
            
        except Exception as e:
            logger.error(f"Technical analysis error for {symbol}: {e}")

class AgentManager:
    """Manages all AI trading agents"""
    
    def __init__(self):
        self.agents: Dict[str, AbstractAgent] = {}
        self.running = False
        
    async def start_all_agents(self):
        """Start all AI trading agents"""
        logger.info("Starting AI Agent Manager...")
        
        # Initialize agents
        self.agents = {
            "market_watcher": MarketWatcherAgent(),
            "strategy_researcher": StrategyResearcher(),
            "risk_officer": RiskOfficerAgent(),
            "technical_analyst": TechnicalAnalystAgent()
        }
        
        # Start all agents
        for name, agent in self.agents.items():
            try:
                await agent.start()
                logger.info(f"Started {name} agent")
            except Exception as e:
                logger.error(f"Failed to start {name}: {e}")
        
        self.running = True
        logger.info("All AI agents are now operational!")
        
        # Start coordination loop
        asyncio.create_task(self._coordination_loop())
    
    async def stop_all_agents(self):
        """Stop all AI trading agents"""
        logger.info("⏹️ Stopping all AI agents...")
        
        self.running = False
        
        for name, agent in self.agents.items():
            try:
                await agent.stop()
                logger.info(f"⏹️ Stopped {name} agent")
            except Exception as e:
                logger.error(f"Error stopping {name}: {e}")
        
        logger.info("🛑 All AI agents stopped")
    
    async def _coordination_loop(self):
        """Coordinate agent activities"""
        while self.running:
            try:
                # Check agent health
                await self._health_check()
                
                # Coordinate agent decisions
                await self._coordinate_decisions()
                
                await asyncio.sleep(30)  # Coordinate every 30 seconds
                
            except Exception as e:
                logger.error(f"Coordination error: {e}")
                await asyncio.sleep(10)
    
    async def _health_check(self):
        """Check health of all agents"""
        for name, agent in self.agents.items():
            if not agent._running:
                logger.warning(f"⚠️ Agent {name} is not running")
    
    async def _coordinate_decisions(self):
        """Coordinate decisions between agents"""
        # This is where multi-agent coordination would happen
        # For now, just log coordination status
        active_agents = sum(1 for agent in self.agents.values() if agent._running)
        logger.debug(f"🤝 Coordinating {active_agents} active agents")
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get status of all agents"""
        status = {}
        for name, agent in self.agents.items():
            status[name] = {
                "running": agent._running,
                "name": agent.NAME
            }
        return status

# Global agent manager
agent_manager = AgentManager()

async def start_ai_agents():
    """Start all AI trading agents"""
    await agent_manager.start_all_agents()

async def stop_ai_agents():
    """Stop all AI trading agents"""
    await agent_manager.stop_all_agents()

if __name__ == "__main__":
    # Test the agent manager
    async def test_agents():
        print("🚀 Testing AI Agent Manager...")
        
        try:
            await start_ai_agents()
            
            # Run for 2 minutes
            await asyncio.sleep(120)
            
        finally:
            await stop_ai_agents()
    
    asyncio.run(test_agents())
