version: '3.8'

services:
  # =============================================================================
  # DATABASE SERVICES
  # =============================================================================
  
  # PostgreSQL - Main transactional database
  postgres:
    image: postgres:15-alpine
    container_name: noryon_postgres
    environment:
      POSTGRES_DB: noryon_trading
      POSTGRES_USER: noryon
      POSTGRES_PASSWORD: noryon123
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-postgres.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U noryon -d noryon_trading"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - noryon_network

  # Redis - Caching and message broker
  redis:
    image: redis:7-alpine
    container_name: noryon_redis
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - noryon_network

  # ClickHouse - Analytics and time-series data
  clickhouse:
    image: clickhouse/clickhouse-server:23.8-alpine
    container_name: noryon_clickhouse
    ports:
      - "8123:8123"  # HTTP interface
      - "9000:9000"  # Native interface
    volumes:
      - clickhouse_data:/var/lib/clickhouse
      - clickhouse_logs:/var/log/clickhouse-server
      - ./config/clickhouse-config.xml:/etc/clickhouse-server/config.xml
      - ./config/clickhouse-users.xml:/etc/clickhouse-server/users.xml
    environment:
      CLICKHOUSE_DB: noryon_analytics
      CLICKHOUSE_USER: noryon
      CLICKHOUSE_PASSWORD: noryon123
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - noryon_network

  # MongoDB - Document storage for logs and unstructured data
  mongodb:
    image: mongo:7-jammy
    container_name: noryon_mongodb
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
      - ./scripts/init-mongo.js:/docker-entrypoint-initdb.d/init.js
    environment:
      MONGO_INITDB_ROOT_USERNAME: noryon
      MONGO_INITDB_ROOT_PASSWORD: noryon123
      MONGO_INITDB_DATABASE: noryon_logs
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - noryon_network

  # =============================================================================
  # MONITORING & OBSERVABILITY
  # =============================================================================

  # Prometheus - Metrics collection
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: noryon_prometheus
    ports:
      - "9090:9090"
    volumes:
      - prometheus_data:/prometheus
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./config/alert_rules.yml:/etc/prometheus/alert_rules.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - noryon_network

  # Grafana - Monitoring dashboards
  grafana:
    image: grafana/grafana:10.1.0
    container_name: noryon_grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: noryon123
      GF_USERS_ALLOW_SIGN_UP: false
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - noryon_network
    depends_on:
      - prometheus

  # AlertManager - Alert handling
  alertmanager:
    image: prom/alertmanager:v0.26.0
    container_name: noryon_alertmanager
    ports:
      - "9093:9093"
    volumes:
      - alertmanager_data:/alertmanager
      - ./config/alertmanager.yml:/etc/alertmanager/alertmanager.yml
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9093/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - noryon_network

  # Node Exporter - System metrics
  node-exporter:
    image: prom/node-exporter:v1.6.1
    container_name: noryon_node_exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    restart: unless-stopped
    networks:
      - noryon_network

  # cAdvisor - Container metrics
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.47.0
    container_name: noryon_cadvisor
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg
    restart: unless-stopped
    networks:
      - noryon_network

  # Nginx - Reverse proxy and load balancer
  nginx:
    image: nginx:1.25-alpine
    container_name: noryon_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf
      - ./config/ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - noryon_network
    depends_on:
      - grafana
      - prometheus

  # Elasticsearch - Log aggregation and search
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.10.0
    container_name: noryon_elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - noryon_network

  # Kibana - Log visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.10.0
    container_name: noryon_kibana
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    volumes:
      - kibana_data:/usr/share/kibana/data
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - noryon_network
    depends_on:
      - elasticsearch

  # Logstash - Log processing pipeline
  logstash:
    image: docker.elastic.co/logstash/logstash:8.10.0
    container_name: noryon_logstash
    ports:
      - "5044:5044"
      - "9600:9600"
    volumes:
      - ./config/logstash/pipeline:/usr/share/logstash/pipeline
      - ./config/logstash/config:/usr/share/logstash/config
      - logstash_data:/usr/share/logstash/data
    environment:
      LS_JAVA_OPTS: "-Xmx256m -Xms256m"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9600 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - noryon_network
    depends_on:
      - elasticsearch

  # Jaeger - Distributed tracing
  jaeger:
    image: jaegertracing/all-in-one:1.49
    container_name: noryon_jaeger
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # HTTP collector
      - "14250:14250"  # gRPC collector
    environment:
      COLLECTOR_OTLP_ENABLED: true
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:16686/"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - noryon_network

  # MinIO - S3-compatible object storage for backups
  minio:
    image: minio/minio:RELEASE.2023-09-30T07-02-29Z
    container_name: noryon_minio
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    environment:
      MINIO_ROOT_USER: noryon
      MINIO_ROOT_PASSWORD: noryon123
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - noryon_network

  # Portainer - Container management UI
  portainer:
    image: portainer/portainer-ce:2.19.1
    container_name: noryon_portainer
    ports:
      - "9443:9443"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_data:/data
    restart: unless-stopped
    networks:
      - noryon_network

# =============================================================================
# VOLUMES
# =============================================================================
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  clickhouse_data:
    driver: local
  clickhouse_logs:
    driver: local
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  alertmanager_data:
    driver: local
  nginx_logs:
    driver: local
  elasticsearch_data:
    driver: local
  kibana_data:
    driver: local
  logstash_data:
    driver: local
  minio_data:
    driver: local
  portainer_data:
    driver: local

# =============================================================================
# NETWORKS
# =============================================================================
networks:
  noryon_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# =============================================================================
# END OF FILE
# ============================================================================= 