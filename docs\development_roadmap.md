# 📅 NORYON Crypto Trading Agents – Development Roadmap (12-Week Sprint Plan)

This roadmap is 100 % practical. Every task ends with running code, passing tests, and concrete deliverables.

---
## 🗂️ Sprint 0 (Prep Week)
| Day | Task | Deliverable |
|-----|------|-------------|
| 1 | Finalise `.env` with real API keys (Binance testnet first) | Working `.env` committed to **local** only |
| 2 | Run `python setup.py` end-to-end | All Docker services **healthy** in `docker ps` |
| 3 | Git repository initialised | Remote repo with `main` branch |

---
## 🏗️ Sprint 1 (Week 1) – Core Infrastructure
| Day | Task | Owner | Acceptance Tests |
|-----|------|-------|------------------|
| 1-2 | Implement **DatabaseManager** (Postgres, Redis, Mongo, ClickHouse) | Backend | `pytest tests/unit/test_database.py` passes |
| 3-4 | Implement **MarketDataEngine** ingesting BTC/ETH from Binance WebSocket | Data Team | 1 M ticks/hour stored in ClickHouse without loss |
| 5 | CI pipeline (GitHub Actions) with `black`, `flake8`, `pytest` | DevOps | PR blocked on failing checks |

Deliverables: running Docker stack + CI.

---
## 🤖 Sprint 2 (Week 2) – Minimal Agent Loop
| Day | Task | Owner | Acceptance |
|-----|------|-------|------------|
| 1 | Implement **BaseAgent** class with Redis pub/sub | Backend | `pytest tests/unit/test_base_agent.py` |
| 2 | Implement **MarketWatcher** (granite3.3) using `MarketDataEngine` | Quant | Logs 3+ anomalies in 24 h |
| 3 | Implement **ChiefAnalyst** (command-r) with simple rule-based prompt | Quant | Emits BUY/SELL/HOLD commands on demo data |
| 4 | Implement **TradeExecutor** (falcon3) in Binance testnet | Trading | Order visible in Binance test UI |
| 5 | E2E test: watcher → analyst → executor | QA | `pytest tests/e2e/test_trade_flow.py` |

---
## 📈 Sprint 3 (Week 3) – Risk + Portfolio
| Day | Task | Owner | Acceptance |
|-----|------|-------|------------|
| 1 | Implement **RiskManager** with max 5 % position rule | Risk | Trade rejected when rule violated |
| 2 | Implement **PortfolioManager** tracking P&L | Quant | `/api/v1/portfolio` returns JSON with current holdings |
| 3-4 | Build **FastAPI** routes: health, agents, trades | Backend | `curl /health` returns 200 |
| 5 | Swagger/OpenAPI auto-generated docs | Backend | Docs visible at `/docs` |

---
## 📰 Sprint 4 (Week 4) – News & Sentiment
| Day | Task | Acceptance |
|-----|------|------------|
| 1 | Integrate Twitter API filtered stream (crypto keywords) | 100 tweets/hour stored |
| 2 | Fine-tune `magistral:24b` on sentiment dataset (10 k rows) | ROC-AUC > 0.85 on test set |
| 3 | NewsAnalyst agent publishes sentiment score to Redis | Score pushed within 5 s of tweet |
| 4 | ChiefAnalyst consumes sentiment in decision prompt | Trades align with positive/negative bias in mock test |
| 5 | Write unit tests for NewsAnalyst pipeline | All tests pass |

---
## 📊 Sprint 5 (Week 5) – Strategy Research + Backtesting
| Task | Acceptance |
|------|------------|
| Implement Backtester util using ClickHouse data | 1-year BTC backtest in < 30 s |
| Fine-tune `cogito:32b` to suggest MA crossover params | Suggestion improves Sharpe by 10 % vs baseline |
| StrategyResearcher writes results to Postgres `strategies` table | Entry appears with JSON parameters |

---
## 💰 Sprint 6 (Week 6) – Multi-Asset & Real Money Toggle
| Task | Acceptance |
|------|------------|
| Add ETH, BNB, SOL symbol support end-to-end | MarketWatcher publishes, Executor trades |
| Implement live/paper toggle in config | Setting `LIVE_TRADING=true` routes to real keys |
| RiskManager retrieves balances from exchange and recalculates VaR | `/risk` endpoint shows updated VaR |

---
## 🛡️ Sprint 7 (Week 7) – Compliance & Auditor
| Task | Acceptance |
|------|------------|
| Implement Auditor agent writing immutable logs to Mongo | 100 % of Redis messages mirrored |
| Daily PDF report via `reportlab` emailed (SendGrid) | Email appears in inbox at 00:05 UTC |

---
## 📱 Sprint 8 (Week 8) – Dashboard & Alerts
| Task | Acceptance |
|------|------------|
| Build React dashboard (Next.js) showing live P&L | Websocket updates < 1 s latency |
| Telegram bot integration for trade alerts | Message received within 2 s after fill |
| Grafana dashboards for system metrics | CPU, mem, Redis QPS visible |

---
## 🧠 Sprint 9 (Week 9) – ML Enhancements
| Task | Acceptance |
|------|------------|
| Train LSTM price predictor with 30-day horizon | MAPE < 5 % on validation |
| Integrate predictor into ChiefAnalyst prompt | Backtest shows Sharpe +0.2 |

---
## 🚀 Sprint 10 (Week 10) – Performance & Security Hardening
| Task | Acceptance |
|------|------------|
| Docker image slim (-alpine, multi-stage) | Image size < 800 MB |
| Enable TLS on FastAPI via Nginx reverse proxy | Qualys SSL Labs grade A |
| Add 2FA login for dashboard | Successful OTP flow |

---
## 📦 Sprint 11 (Week 11) – Exchange Expansion
| Task | Acceptance |
|------|------------|
| Integrate Kraken and Bybit connectors | Live price + paper orders OK |
| Abstract Exchange Interface + tests | `pytest tests/unit/test_exchange_interface.py` |

---
## 🏁 Sprint 12 (Week 12) – Go-Live & Review
| Task | Acceptance |
|------|------------|
| Run 7-day live trading with small capital | Daily P&L report delivered |
| Post-mortem and optimisation backlog created | Document stored in `/docs/post_mortems/` |
| Tag `v1.0` release in Git | Git tag visible |

---
# Feature Backlog (Post-v1)
* Options trading module (Deribit)
* DeFi liquidity provisioning (Uniswap V3)
* On-chain analytics agent
* Reinforcement learning position scheduler
* Mobile app (Flutter) for trade approvals

---
**All tasks above are practical, time-boxed, and deliver visible outcomes—no theory, only shippable software.** 