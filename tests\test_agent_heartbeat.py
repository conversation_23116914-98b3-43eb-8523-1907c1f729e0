"""Test AbstractAgent heartbeat and Prometheus counter increment."""

import asyncio
import json
from unittest.mock import AsyncMock

import pytest

from src.agents.base import AbstractAgent, HEARTBEAT_INTERVAL
from src.core.metrics import AGENT_HEARTBEATS


class DummyAgent(AbstractAgent):
    NAME = "dummy"


@pytest.mark.asyncio
async def test_agent_heartbeat_increments_counter(monkeypatch):  # type: ignore[missing-annotations]
    # Arrange
    dummy = DummyAgent()

    # Mock redis publish to avoid real Redis dependency
    publish_calls = []

    async def _fake_publish(channel, message):  # type: ignore[missing-annotations]
        publish_calls.append((channel, json.loads(message)))
        return 1  # number of clients that received the message

    monkeypatch.setattr(dummy._redis, "publish", _fake_publish)

    # Act
    await dummy.start()
    await asyncio.sleep(HEARTBEAT_INTERVAL * 2 + 0.2)
    await dummy.stop()

    # Assert: at least 2 heartbeats published
    assert len(publish_calls) >= 2
    # Counter should have increased by at least same amount
    assert AGENT_HEARTBEATS._value.get() >= 2  # pylint: disable=protected-access 