"""
Real Data Ingestion Service
Handles real-time market data ingestion and storage
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List
import json

from src.db.clickhouse import get_client
from src.services.binance_connector import binance_connector
from src.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

class DataIngestionService:
    """Service for ingesting and storing real market data"""
    
    def __init__(self):
        self.running = False
        self.clickhouse_client = None
        self.batch_size = 100
        self.batch_buffer = []
        self.last_flush = datetime.now()
        
    async def start(self):
        """Start the data ingestion service"""
        self.running = True
        logger.info("🚀 Starting Data Ingestion Service")
        
        try:
            # Initialize ClickHouse client
            self.clickhouse_client = get_client()
            
            # Create tables if they don't exist
            await self._create_tables()
            
            # Subscribe to Binance data
            binance_connector.add_subscriber(self._handle_tick_data)
            
            # Start Binance connector
            await binance_connector.start()
            
            # Start batch processing
            asyncio.create_task(self._batch_processor())
            
            logger.info("✅ Data Ingestion Service started")
            
        except Exception as e:
            logger.error(f"Failed to start Data Ingestion Service: {e}")
            raise
    
    def stop(self):
        """Stop the data ingestion service"""
        self.running = False
        binance_connector.stop()
        logger.info("⏹️ Data Ingestion Service stopped")
    
    async def _create_tables(self):
        """Create ClickHouse tables for market data"""
        try:
            # Create ticks table
            create_ticks_sql = """
            CREATE TABLE IF NOT EXISTS ticks (
                symbol String,
                ts DateTime64(3),
                last Float64,
                volume Float64,
                bid Float64,
                ask Float64,
                high_24h Float64,
                low_24h Float64,
                change_24h Float64,
                source String
            ) ENGINE = MergeTree()
            ORDER BY (symbol, ts)
            PARTITION BY toYYYYMM(ts)
            """
            
            # Create candles materialized view
            create_candles_sql = """
            CREATE MATERIALIZED VIEW IF NOT EXISTS candles_1m
            ENGINE = MergeTree()
            ORDER BY (symbol, ts)
            PARTITION BY toYYYYMM(ts)
            AS SELECT
                symbol,
                toStartOfMinute(ts) as ts,
                argMin(last, ts) as open,
                max(last) as high,
                min(last) as low,
                argMax(last, ts) as close,
                sum(volume) as volume
            FROM ticks
            GROUP BY symbol, toStartOfMinute(ts)
            """
            
            # Create trades table for AI analysis results
            create_analysis_sql = """
            CREATE TABLE IF NOT EXISTS ai_analysis (
                symbol String,
                ts DateTime64(3),
                agent_name String,
                analysis_type String,
                result String,
                confidence Float64,
                metadata String
            ) ENGINE = MergeTree()
            ORDER BY (symbol, ts, agent_name)
            PARTITION BY toYYYYMM(ts)
            """
            
            self.clickhouse_client.execute(create_ticks_sql)
            self.clickhouse_client.execute(create_candles_sql)
            self.clickhouse_client.execute(create_analysis_sql)
            
            logger.info("✅ ClickHouse tables created/verified")
            
        except Exception as e:
            logger.error(f"Failed to create ClickHouse tables: {e}")
            raise
    
    async def _handle_tick_data(self, tick: Dict[str, Any]):
        """Handle incoming tick data from Binance"""
        try:
            # Format tick for ClickHouse
            formatted_tick = {
                "symbol": tick["symbol"],
                "ts": datetime.now(timezone.utc),
                "last": tick["price"],
                "volume": tick["volume"],
                "bid": tick["bid"],
                "ask": tick["ask"],
                "high_24h": tick["high_24h"],
                "low_24h": tick["low_24h"],
                "change_24h": tick["change_24h"],
                "source": tick["source"]
            }
            
            # Add to batch buffer
            self.batch_buffer.append(formatted_tick)
            
            # Log significant price movements
            if abs(tick["change_24h"]) > 2.0:
                logger.info(f"📈 {tick['symbol']}: ${tick['price']:,.2f} ({tick['change_24h']:+.2f}%)")
            
        except Exception as e:
            logger.error(f"Error handling tick data: {e}")
    
    async def _batch_processor(self):
        """Process batched data for efficient ClickHouse insertion"""
        while self.running:
            try:
                # Check if we should flush the batch
                should_flush = (
                    len(self.batch_buffer) >= self.batch_size or
                    (datetime.now() - self.last_flush).seconds >= 10  # Flush every 10 seconds
                )
                
                if should_flush and self.batch_buffer:
                    await self._flush_batch()
                
                await asyncio.sleep(1)  # Check every second
                
            except Exception as e:
                logger.error(f"Batch processor error: {e}")
                await asyncio.sleep(5)
    
    async def _flush_batch(self):
        """Flush the current batch to ClickHouse"""
        if not self.batch_buffer:
            return
            
        try:
            # Insert batch into ClickHouse
            self.clickhouse_client.execute(
                "INSERT INTO ticks VALUES",
                self.batch_buffer
            )
            
            logger.debug(f"✅ Inserted {len(self.batch_buffer)} ticks into ClickHouse")
            
            # Clear buffer and update timestamp
            self.batch_buffer.clear()
            self.last_flush = datetime.now()
            
        except Exception as e:
            logger.error(f"Failed to flush batch to ClickHouse: {e}")
            # Keep the buffer for retry
    
    async def store_ai_analysis(self, symbol: str, agent_name: str, analysis_type: str, 
                               result: str, confidence: float = 0.0, metadata: Dict[str, Any] = None):
        """Store AI analysis results"""
        try:
            analysis_record = {
                "symbol": symbol,
                "ts": datetime.now(timezone.utc),
                "agent_name": agent_name,
                "analysis_type": analysis_type,
                "result": result,
                "confidence": confidence,
                "metadata": json.dumps(metadata or {})
            }
            
            self.clickhouse_client.execute(
                "INSERT INTO ai_analysis VALUES",
                [analysis_record]
            )
            
            logger.debug(f"✅ Stored AI analysis: {agent_name} -> {symbol}")
            
        except Exception as e:
            logger.error(f"Failed to store AI analysis: {e}")
    
    def get_latest_ticks(self, symbol: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get latest ticks for a symbol"""
        try:
            query = """
            SELECT symbol, ts, last, volume, bid, ask, high_24h, low_24h, change_24h, source
            FROM ticks
            WHERE symbol = %(symbol)s
            ORDER BY ts DESC
            LIMIT %(limit)s
            """
            
            result = self.clickhouse_client.execute(query, {"symbol": symbol, "limit": limit})
            
            ticks = []
            for row in result:
                ticks.append({
                    "symbol": row[0],
                    "timestamp": row[1].isoformat(),
                    "price": row[2],
                    "volume": row[3],
                    "bid": row[4],
                    "ask": row[5],
                    "high_24h": row[6],
                    "low_24h": row[7],
                    "change_24h": row[8],
                    "source": row[9]
                })
            
            return ticks
            
        except Exception as e:
            logger.error(f"Failed to get latest ticks: {e}")
            return []
    
    def get_candles(self, symbol: str, interval: str = "1m", limit: int = 100) -> List[Dict[str, Any]]:
        """Get candlestick data"""
        try:
            # For now, just use 1m candles from materialized view
            query = """
            SELECT symbol, ts, open, high, low, close, volume
            FROM candles_1m
            WHERE symbol = %(symbol)s
            ORDER BY ts DESC
            LIMIT %(limit)s
            """
            
            result = self.clickhouse_client.execute(query, {"symbol": symbol, "limit": limit})
            
            candles = []
            for row in result:
                candles.append({
                    "symbol": row[0],
                    "timestamp": row[1].isoformat(),
                    "open": row[2],
                    "high": row[3],
                    "low": row[4],
                    "close": row[5],
                    "volume": row[6]
                })
            
            return list(reversed(candles))  # Return in chronological order
            
        except Exception as e:
            logger.error(f"Failed to get candles: {e}")
            return []
    
    def get_market_stats(self) -> Dict[str, Any]:
        """Get market statistics"""
        try:
            query = """
            SELECT 
                symbol,
                count() as tick_count,
                min(ts) as first_tick,
                max(ts) as last_tick,
                avg(last) as avg_price,
                max(last) as max_price,
                min(last) as min_price
            FROM ticks
            WHERE ts > now() - INTERVAL 1 HOUR
            GROUP BY symbol
            ORDER BY tick_count DESC
            """
            
            result = self.clickhouse_client.execute(query)
            
            stats = {}
            for row in result:
                stats[row[0]] = {
                    "tick_count": row[1],
                    "first_tick": row[2].isoformat(),
                    "last_tick": row[3].isoformat(),
                    "avg_price": row[4],
                    "max_price": row[5],
                    "min_price": row[6]
                }
            
            return {
                "symbols": stats,
                "total_symbols": len(stats),
                "total_ticks": sum(s["tick_count"] for s in stats.values())
            }
            
        except Exception as e:
            logger.error(f"Failed to get market stats: {e}")
            return {"symbols": {}, "total_symbols": 0, "total_ticks": 0}

# Global data ingestion service
data_ingestion = DataIngestionService()

async def start_data_ingestion():
    """Start the data ingestion system"""
    await data_ingestion.start()

def stop_data_ingestion():
    """Stop the data ingestion system"""
    data_ingestion.stop()

if __name__ == "__main__":
    # Test the data ingestion service
    async def test_ingestion():
        print("🚀 Testing Data Ingestion Service...")
        
        try:
            await data_ingestion.start()
            
            # Run for 60 seconds
            await asyncio.sleep(60)
            
        except KeyboardInterrupt:
            print("⏹️ Test stopped")
        finally:
            data_ingestion.stop()
    
    asyncio.run(test_ingestion())
