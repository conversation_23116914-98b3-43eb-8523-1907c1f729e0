# 🤖 NORYON – AI Development Plan (Practical Guide)

This document drills into **exactly** how we will turn your 9 generic Ollama LLMs into specialised, production-ready crypto-trading agents.
Every step has a deliverable, an owner, success metrics, and required hardware.

---
## 0. Hardware & Environment Checklist
| Resource | Min Spec | Purpose |
|----------|----------|---------|
| GPU      | 1× RTX 4090 (24 GB) **or** 2× 3090 | Finetune one 24-35 B model with 4-bit LoRA |
| CPU      | ≥ 8 cores | Data preprocessing & backtesting |
| RAM      | 64 GB     | Loading large datasets |
| Disk     | 2 TB SSD  | Store raw + processed data, model checkpoints |
| Software | Ubuntu 22.04, CUDA 12, Python 3.11 | Standard ML stack |

> If you only have a single 16 GB GPU, restrict fine-tuning to 8-14 B models and use 8-bit LoRA. All scripts allow `--model_size small`.

---
## 1. Data Pipeline (Week 1)
| Step | Task | Deliverable | Tooling |
|------|------|-------------|---------|
| 1.1 | **Historical Price** dump (BTC, ETH, BNB, SOL, ADA) | Parquet files in `data/raw/prices/` | Binance REST, Kraken CSV |
| 1.2 | **News & Tweets** (2016-2024) scrape | JSONL in `data/raw/news/` with timestamp, text, URL | Twitter API v2, NewsAPI.org |
| 1.3 | **Label Sentiment** automatically (VADER + price delta) | `sentiment.csv` with target ∈ {-1,0,+1} | Python script `scripts/label_sentiment.py` |
| 1.4 | **Chart Pattern** auto-label generator | PNG images + `patterns.csv` | Custom TA-Lib scanner |
| 1.5 | CI job `make validate-data` | Pytest passes schema checks | Great Expectations |

Success Metric: ✅ `python scripts/data_quality_check.py` returns "All Good".

---
## 2. Finetuning Plan (Weeks 2-4)

### 2.1 Model-to-Task Mapping
| Agent | Base Model | Domain Task | Target Metric |
|-------|------------|-------------|---------------|
| NewsSentiment | magistral:24b | Crypto news sentiment classification | ROC-AUC ≥ 0.85 |
| TechnicalAnalyst | gemma3:27b | Chart pattern → text explanation | BLEU ≥ 0.35 |
| StrategyResearcher | cogito:32b | Param suggestion QA | Exact-match ≥ 0.6 |
| ChiefAnalyst | command-r:35b | Multi-input decision (JSON) | Accuracy ≥ 0.7 versus rule baseline |
| PortfolioMgr | qwen3:32b | Rebalance reasoning | Pass unit tests |
| RiskOfficer | mistral-small:24b | Risk rule justification | 0 critical errors with test suite |
| MarketWatcher | granite3.3:8b | Anomaly description | Precision ≥ 0.8 |
| TradeExecutor | falcon3:10b | Exchange API instruction following | 100 % valid JSON cmds |
| Auditor | deepseek-r1 | Compliance log summarisation | Rouge-L ≥ 0.3 |

### 2.2 Training Workflow
```
1. ./scripts/prepare_dataset.py  --agent news
2. ./scripts/finetune_lora.py    --model magistral:24b  --dataset data/processed/news_sentiment.jsonl  \
                                  --epochs 3 --bits 4 --output models/magistral_sentiment
3. ./scripts/evaluate.py         --model models/magistral_sentiment --task sentiment
4. If metric >= target  ✅  -> move to `ollama import`  # creates local tag `magistral:24b-sentiment`
   Else 🔁 tweak hyper-params
```

Each script is idempotent and has CLI `--help` docs.

### 2.3 Tracking & Reproducibility
* **MLflow** for experiment tracking (`mlruns/` committed to S3 bucket).
* **DVC** optional remote storage for large datasets.
* Git tag `finetune/<agent>/<date>` after each converged run.

---
## 3. Integration & Verification (Week 5)
| Task | Procedure | Pass Criteria |
|------|-----------|---------------|
| Replace Base model calls with finetuned tags in `agents/*.py` | Update config `MODEL_NEWS_ANALYST=magistral:24b-sentiment` | App starts w/out OOM |
| Regression tests on sandbox | `pytest tests/integration/test_full_loop.py` | PnL ≥ demo baseline |
| Latency benchmark | `scripts/benchmark_latency.py` | Each agent < 200 ms per call |

---
## 4. Continuous Training (Week 6 onward)
| Component | Schedule | Trigger |
|-----------|----------|---------|
| Daily data sync | 02:00 UTC | Airflow DAG `sync_data` |
| Weekly incremental fine-tune | Sunday 03:00 | DAG `finetune_incremental` – only if new data > 10 k rows |
| Model promotion | Manual approve in dashboard | If eval metric improves > 1 % |

---
## 5. Deployment to Ollama (Local) & Staging
* Use `ollama create` to tag each finetuned checkpoint.
* CI step pushes model files to private HuggingFace repo as tarballs (backup).
* Staging environment loads `MODEL_*_TAG` from `.env.staging` and runs full E2E tests.

---
## 6. Tooling Summary
| Tool | Why | Config File |
|------|-----|-------------|
| **HuggingFace Transformers** | LoRA fine-tune | `scripts/finetune_lora.py` |
| **PEFT** | Parameter-efficient tuning | same as above |
| **BitsAndBytes** | 4-bit quantization | auto in finetune script |
| **MLflow** | Experiment tracking | `mlflow.yaml` |
| **ClickHouse** | Fast historical data | `docker-compose.yml` |
| **Great Expectations** | Data QC | `great_expectations/` |
| **Airflow** | Scheduled pipelines | `docker-compose.yml` Airflow svc |

---
## 7. Acceptance Gates
* ✅ **Unit tests pass** (`pytest -q`)
* ✅ **Data quality checks pass** (`make validate-data`)
* ✅ **Eval metrics meet targets** (see §2.1)
* ✅ **Latency below thresholds**
* ✅ **Risk Officer veto logic works in dry-run**

If all checks are ✅ the model tag is promoted to `production` in `.env`.

---
## 8. Timeline Recap
| Week | Milestone |
|------|-----------|
| 1 | Raw data collected & validated |
| 2-4 | All nine models fine-tuned & evaluated |
| 5 | Models integrated into agents → sandbox loop green |
| 6+ | Continuous training pipeline live |

---
**No fluff. Every bullet = script, code, or measurable metric.** 