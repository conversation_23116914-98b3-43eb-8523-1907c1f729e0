"""Market Data Engine - Real-time price feed ingestion.

This service connects to crypto exchanges via CCXT, streams price ticks,
and distributes them to ClickHouse (storage) and Redis (pub/sub).

Sprint-1 implementation uses REST polling as a starting point, with
WebSocket support planned for Sprint-2.
"""

from __future__ import annotations

import asyncio
import json
import time
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

import ccxt.async_support as ccxt
import redis.asyncio as aioredis
from loguru import logger
from tenacity import retry, stop_after_attempt, wait_exponential

from src.core.config import Settings
from src.core.metrics import MARKET_DATA_ERRORS, TICK_LATENCY, TICKS_INGESTED
from src.db.clickhouse import get_client

settings = Settings()


class MarketDataEngine:
    """Ingests real-time market data from crypto exchanges."""

    def __init__(self) -> None:
        self.redis = aioredis.from_url(settings.REDIS_URL, decode_responses=True)
        self.clickhouse = get_client()
        self.exchanges: Dict[str, ccxt.Exchange] = {}
        self.running = False
        self._tasks: List[asyncio.Task] = []
        self._tick_buffer: List[Dict[str, Any]] = []
        self._last_flush = time.time()

    async def start(self) -> None:
        """Initialize exchanges and start ingestion loops."""
        logger.info("Starting Market Data Engine...")
        self.running = True
        
        # Initialize Binance testnet
        self.exchanges["binance"] = ccxt.binance({
            "apiKey": settings.BINANCE_API_KEY,
            "secret": settings.BINANCE_SECRET_KEY,
            "enableRateLimit": True,
            "options": {
                "defaultType": "spot",
                "sandboxMode": settings.BINANCE_TESTNET,
            },
        })
        
        # Parse trading pairs
        pairs = [p.strip() for p in settings.TRADING_PAIRS.split(",") if p.strip()]
        logger.info(f"Monitoring pairs: {pairs}")
        
        # Start ingestion tasks
        for symbol in pairs:
            task = asyncio.create_task(self._ingest_loop("binance", symbol))
            self._tasks.append(task)
        
        # Start buffer flush task
        flush_task = asyncio.create_task(self._flush_loop())
        self._tasks.append(flush_task)

    async def stop(self) -> None:
        """Gracefully shutdown all tasks."""
        logger.info("Stopping Market Data Engine...")
        self.running = False
        
        # Cancel all tasks
        for task in self._tasks:
            task.cancel()
        
        # Wait for cancellation
        await asyncio.gather(*self._tasks, return_exceptions=True)
        
        # Close connections
        for exchange in self.exchanges.values():
            await exchange.close()
        await self.redis.close()
        
        logger.info("Market Data Engine stopped")

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _fetch_ticker(self, exchange_id: str, symbol: str) -> Dict[str, Any]:
        """Fetch latest ticker data with retry logic."""
        exchange = self.exchanges[exchange_id]
        ticker = await exchange.fetch_ticker(symbol)
        return ticker

    async def _ingest_loop(self, exchange_id: str, symbol: str) -> None:
        """Main ingestion loop for a single symbol."""
        logger.info(f"Starting ingestion for {symbol} on {exchange_id}")
        
        while self.running:
            try:
                # Fetch ticker
                ticker = await self._fetch_ticker(exchange_id, symbol)
                
                # Extract data
                ts = datetime.fromtimestamp(ticker["timestamp"] / 1000, tz=timezone.utc)
                tick = {
                    "symbol": symbol,
                    "exchange": exchange_id,
                    "ts": ts,
                    "bid": ticker.get("bid", 0.0),
                    "ask": ticker.get("ask", 0.0),
                    "last": ticker.get("last", 0.0),
                    "volume": ticker.get("baseVolume", 0.0),
                }
                
                # Calculate latency
                latency = (time.time() * 1000 - ticker["timestamp"]) / 1000
                TICK_LATENCY.labels(symbol=symbol).observe(latency)
                
                # Buffer for ClickHouse
                self._tick_buffer.append(tick)
                
                # Publish to Redis pubsub
                channel = f"ticks:{symbol}"
                tick_json = json.dumps({
                    "ts": ts.isoformat(),
                    "bid": tick["bid"],
                    "ask": tick["ask"],
                    "last": tick["last"],
                    "volume": tick["volume"],
                })
                await self.redis.publish(channel, tick_json)
                
                # Also save latest tick for API queries
                await self.redis.set(f"tick:latest:{symbol}", tick_json, ex=60)
                
                # Update metrics
                TICKS_INGESTED.labels(symbol=symbol, exchange=exchange_id).inc()
                
                # Rate limiting (10 requests/second max)
                await asyncio.sleep(0.1)
                
            except Exception as exc:
                logger.error(f"Error ingesting {symbol}: {exc}")
                MARKET_DATA_ERRORS.labels(
                    exchange=exchange_id,
                    error_type=type(exc).__name__
                ).inc()
                await asyncio.sleep(1)  # Back off on error

    async def _flush_loop(self) -> None:
        """Periodically flush tick buffer to ClickHouse."""
        while self.running:
            try:
                await asyncio.sleep(1)  # Check every second
                
                # Flush if buffer is large or time elapsed
                if len(self._tick_buffer) >= 100 or (time.time() - self._last_flush) > 5:
                    await self._flush_buffer()
                    
            except Exception as exc:
                logger.error(f"Error in flush loop: {exc}")

    async def _flush_buffer(self) -> None:
        """Write buffered ticks to ClickHouse."""
        if not self._tick_buffer:
            return
            
        try:
            # Copy and clear buffer
            ticks = self._tick_buffer[:]
            self._tick_buffer.clear()
            
            # Bulk insert
            self.clickhouse.execute(
                "INSERT INTO ticks (symbol, exchange, ts, bid, ask, last, volume) VALUES",
                ticks
            )
            
            logger.debug(f"Flushed {len(ticks)} ticks to ClickHouse")
            self._last_flush = time.time()
            
        except Exception as exc:
            logger.error(f"Failed to flush to ClickHouse: {exc}")
            # Re-add to buffer on failure
            self._tick_buffer.extend(ticks)


async def main() -> None:
    """Standalone entry point for testing."""
    from src.core.logger import configure
    configure(file_sink=True)
    
    engine = MarketDataEngine()
    
    try:
        await engine.start()
        # Run for 60 seconds in test mode
        await asyncio.sleep(60)
    finally:
        await engine.stop()


if __name__ == "__main__":
    asyncio.run(main()) 