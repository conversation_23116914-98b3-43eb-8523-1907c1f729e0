"""Tests for Strategy Researcher agent."""

import asyncio
import json
from collections import deque
from unittest.mock import Async<PERSON><PERSON>, MagicMock, patch

import pytest
import pandas as pd

from src.agents.strategy_researcher import StrategyResearcher


@pytest.mark.asyncio
async def test_strategy_researcher_initialization():
    """Test agent initializes with correct parameters."""
    agent = StrategyResearcher()
    
    assert agent.NAME == "strategy_researcher"
    assert agent.fast_period == 50
    assert agent.slow_period == 200
    assert agent.window_size == 250
    assert isinstance(agent.price_windows, dict)
    assert isinstance(agent.last_signals, dict)


@pytest.mark.asyncio
async def test_sma_crossover_detection():
    """Test SMA crossover signal generation."""
    agent = StrategyResearcher()
    
    # Mock Redis publish and set
    agent._redis = MagicMock()
    agent._redis.publish = AsyncMock()
    agent._redis.set = AsyncMock()
    
    # Create a very specific crossover scenario
    # We'll create data where we can precisely control the crossover moment
    prices = []
    
    # Create a scenario where the crossover happens at the very end
    # Start with declining prices to get fast SMA below slow SMA
    for i in range(200):
        prices.append(100.0 - i * 0.15)  # 100 down to 70.15
    
    # Add more declining prices to ensure fast < slow at the "previous" position
    for i in range(20):
        prices.append(70.0 - i * 0.1)  # Continue decline to 68.1
    
    # Add a few high prices to start moving fast SMA up but keep it below slow
    for i in range(4):
        prices.append(200.0)  # Start the upward movement
    
    # Add one final extreme price to push fast SMA above slow SMA at the very end
    prices.append(500.0)  # Final spike to trigger crossover
    
    # Set up price window
    agent.price_windows["BTC/USDT"] = deque(prices, maxlen=agent.window_size)
    

    
    # Check for signal
    await agent._check_signal("BTC/USDT")
    
    # Should have published a BUY signal
    agent._redis.publish.assert_called_once()
    call_args = agent._redis.publish.call_args
    assert call_args[0][0] == "signals:BTC/USDT"
    
    signal_data = json.loads(call_args[0][1])
    assert signal_data["side"] == "BUY"
    assert signal_data["symbol"] == "BTC/USDT"
    assert signal_data["strategy"] == "sma_crossover"


@pytest.mark.asyncio
async def test_no_signal_without_crossover():
    """Test no signal is generated without crossover."""
    agent = StrategyResearcher()
    
    # Mock Redis
    agent._redis = MagicMock()
    agent._redis.publish = AsyncMock()
    
    # Create flat price data (no crossover)
    prices = [100.0] * 250
    agent.price_windows["ETH/USDT"] = deque(prices, maxlen=agent.window_size)
    
    # Check for signal
    await agent._check_signal("ETH/USDT")
    
    # Should not publish any signal
    agent._redis.publish.assert_not_called()


@pytest.mark.asyncio
async def test_insufficient_data_handling():
    """Test agent handles insufficient price data gracefully."""
    agent = StrategyResearcher()
    
    # Mock Redis
    agent._redis = MagicMock()
    agent._redis.publish = AsyncMock()
    
    # Only 50 prices (less than slow period)
    prices = [100.0] * 50
    agent.price_windows["SOL/USDT"] = deque(prices, maxlen=agent.window_size)
    
    # Should not crash
    await agent._check_signal("SOL/USDT")
    
    # Should not publish
    agent._redis.publish.assert_not_called() 