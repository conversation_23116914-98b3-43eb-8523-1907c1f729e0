#!/usr/bin/env python3
"""
Noryon V2 - Development Mode
Run the system without external dependencies for testing and development
"""

import asyncio
import logging
import sys
from datetime import datetime
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.core.config import Config
from src.core.logger import setup_logging

class MockDatabaseManager:
    """Mock database manager for development"""
    
    def __init__(self, config):
        self.config = config
        self.data = {}
        
    async def initialize(self):
        print("✅ Mock database initialized")
        
    async def close(self):
        print("✅ Mock database closed")

class MockExchangeManager:
    """Mock exchange manager for development"""
    
    def __init__(self, config):
        self.config = config
        self.mock_prices = {
            "BTC/USDT": 45000.0,
            "ETH/USDT": 3000.0,
            "BNB/USDT": 400.0
        }
        
    async def initialize(self):
        print("✅ Mock exchange connections established")
        
    async def get_ticker(self, symbol):
        return {
            "symbol": symbol,
            "price": self.mock_prices.get(symbol, 100.0),
            "timestamp": datetime.now()
        }

class MockAgent:
    """Mock AI agent for development"""
    
    def __init__(self, name, model_name, config, db_manager=None):
        self.name = name
        self.model_name = model_name
        self.config = config
        self.db_manager = db_manager
        self.running = False
        
    async def initialize(self):
        print(f"✅ Mock agent {self.name} initialized with {self.model_name}")
        
    async def start(self):
        self.running = True
        print(f"✅ Mock agent {self.name} started")
        
    async def stop(self):
        self.running = False
        print(f"✅ Mock agent {self.name} stopped")

class MockSystemMonitor:
    """Mock system monitor for development"""
    
    def __init__(self, config):
        self.config = config
        
    async def start(self):
        print("✅ Mock system monitoring started")

class MockAPIServer:
    """Mock API server for development"""
    
    def __init__(self, orchestrator, config):
        self.orchestrator = orchestrator
        self.config = config
        
    async def start(self):
        print("✅ Mock API server started on port 8000")
        print("📊 Mock Dashboard: http://localhost:8000/dashboard")
        print("📡 Mock API Docs: http://localhost:8000/docs")

class MockOrchestrator:
    """Mock orchestrator for development"""
    
    def __init__(self, agents, db_manager, exchange_manager, config):
        self.agents = agents
        self.db_manager = db_manager
        self.exchange_manager = exchange_manager
        self.config = config
        
    async def initialize(self):
        print("✅ Mock orchestrator initialized")
        
    async def start(self):
        print("✅ Mock orchestrator started")
        # Simulate some trading activity
        asyncio.create_task(self._simulate_trading())
        
    async def _simulate_trading(self):
        """Simulate trading activity for demonstration"""
        while True:
            await asyncio.sleep(10)
            print(f"📊 Mock trading activity at {datetime.now().strftime('%H:%M:%S')}")
            print("   - Market Watcher: BTC/USDT $45,123 (+2.3%)")
            print("   - Technical Analyst: RSI oversold signal detected")
            print("   - Risk Manager: Portfolio risk at 1.2%")
            print("   - Trader: Paper trade executed: BUY 0.01 BTC")

class NoryonDevMode:
    """Development mode for Noryon V2 Trading System"""
    
    def __init__(self):
        self.config = Config()
        self.logger = logging.getLogger(__name__)
        self.running = False
        self.agents = {}
        
    async def initialize(self):
        """Initialize all mock components"""
        try:
            print("🚀 Initializing Noryon V2 in Development Mode...")
            
            # Initialize mock database
            self.db_manager = MockDatabaseManager(self.config)
            await self.db_manager.initialize()
            
            # Initialize mock exchange
            self.exchange_manager = MockExchangeManager(self.config)
            await self.exchange_manager.initialize()
            
            # Initialize mock system monitor
            self.system_monitor = MockSystemMonitor(self.config)
            await self.system_monitor.start()
            
            # Initialize mock agents
            await self._initialize_mock_agents()
            
            # Initialize mock orchestrator
            self.orchestrator = MockOrchestrator(
                agents=self.agents,
                db_manager=self.db_manager,
                exchange_manager=self.exchange_manager,
                config=self.config
            )
            await self.orchestrator.initialize()
            
            # Initialize mock API server
            self.api_server = MockAPIServer(
                orchestrator=self.orchestrator,
                config=self.config
            )
            
            print("🎯 Noryon V2 Development Mode fully initialized!")
            
        except Exception as e:
            print(f"❌ Failed to initialize development mode: {e}")
            raise
    
    async def _initialize_mock_agents(self):
        """Initialize all mock AI agents"""
        
        agent_configs = [
            ('market_watcher', 'llama3.2:3b'),
            ('news_analyst', 'qwen2.5:7b'),
            ('technical_analyst', 'gemma2:9b'),
            ('chief_analyst', 'mistral:7b'),
            ('researcher', 'llama3.2:3b'),
            ('risk_manager', 'qwen2.5:7b'),
            ('trader', 'gemma2:9b'),
            ('portfolio_manager', 'mistral:7b'),
            ('auditor', 'llama3.2:3b')
        ]
        
        for agent_name, model_name in agent_configs:
            agent = MockAgent(
                name=agent_name,
                model_name=model_name,
                config=self.config,
                db_manager=self.db_manager
            )
            await agent.initialize()
            self.agents[agent_name] = agent
    
    async def start(self):
        """Start the development system"""
        try:
            self.running = True
            print("🚀 Starting Noryon V2 Development Mode...")
            
            # Start all mock agents
            for agent_name, agent in self.agents.items():
                await agent.start()
            
            # Start mock orchestrator
            await self.orchestrator.start()
            
            # Start mock API server
            await self.api_server.start()
            
            print("🎯 Noryon V2 Development Mode is now LIVE!")
            print("\n" + "="*80)
            print("DEVELOPMENT MODE FEATURES:")
            print("- Mock AI agents (no Ollama required)")
            print("- Mock database (no PostgreSQL/Redis required)")
            print("- Mock exchange data (no API keys required)")
            print("- Paper trading simulation")
            print("- Real-time mock trading activity")
            print("="*80 + "\n")
            
            # Keep the system running
            while self.running:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            print("\n🛑 Shutting down development mode...")
            await self.shutdown()
        except Exception as e:
            print(f"❌ Error in development mode: {e}")
            await self.shutdown()
    
    async def shutdown(self):
        """Shutdown the development system"""
        try:
            self.running = False
            print("🛑 Shutting down Noryon V2 Development Mode...")
            
            # Stop all agents
            for agent_name, agent in self.agents.items():
                await agent.stop()
            
            # Close database
            if hasattr(self, 'db_manager'):
                await self.db_manager.close()
            
            print("✅ Development mode shutdown complete")
            
        except Exception as e:
            print(f"❌ Error during shutdown: {e}")

async def main():
    """Main entry point for development mode"""
    
    # Setup logging
    setup_logging(level="INFO")
    
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                        NORYON V2 DEVELOPMENT MODE                           ║
║                      Advanced AI Crypto Trading System                      ║
║                                                                              ║
║  🧪 Mock Environment - No External Dependencies Required                     ║
║  🤖 9 Simulated AI Agents                                                    ║
║  📊 Mock Market Data & Trading                                               ║
║  🛡️ Paper Trading Mode                                                       ║
║                                                                              ║
║  Perfect for development, testing, and demonstration!                       ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    # Create and run development system
    dev_system = NoryonDevMode()
    await dev_system.initialize()
    await dev_system.start()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1) 