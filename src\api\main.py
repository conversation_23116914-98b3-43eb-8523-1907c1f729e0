"""FastAPI application entrypoint for Noryon Crypto Trading backend.

This minimal app is part of Sprint-0. It exposes:
  • GET /healthz – basic liveness probe.
  • GET /metrics – Prometheus exposition format.

The implementation intentionally avoids any business logic; that will
be added in later sprints. The goal is to verify the web layer can
start, serve requests, and export metrics under <50 ms latency.
"""

from __future__ import annotations

import logging
import time
from typing import Any, Dict

from fastapi import FastAPI, Response
from fastapi.responses import JSONResponse, PlainTextResponse
from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
from src.core.metrics import (
    REQUEST_COUNT,
    REQUEST_LATENCY,
    APP_READY,
)
from src.api.routes import market

# ---------------------------------------------------------------------------
# Logging
# ---------------------------------------------------------------------------
logger = logging.getLogger("noryon.api")
logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(name)s: %(message)s")

# ---------------------------------------------------------------------------
# Prometheus metrics
# ---------------------------------------------------------------------------

# ---------------------------------------------------------------------------
# FastAPI application
# ---------------------------------------------------------------------------
app = FastAPI(title="Noryon Crypto Trading API", version="0.2.0", docs_url="/docs")

# Include routers
app.include_router(market.router)


@app.middleware("http")
async def add_metrics(request, call_next):  # type: ignore[missing-annotations]
    """Prometheus latency & request counters middleware."""
    start_time = time.perf_counter()
    response: Response | PlainTextResponse | JSONResponse = await call_next(request)
    latency = time.perf_counter() - start_time

    # Record metrics. Use request.url.path without query string for cardinality control.
    path = request.url.path
    REQUEST_LATENCY.labels(request.method, path).observe(latency)
    REQUEST_COUNT.labels(request.method, path, str(response.status_code)).inc()
    return response


@app.on_event("startup")
async def _startup() -> None:  # noqa: D401
    """Runs once when application starts."""
    logger.info("🚀 Noryon API starting …")
    APP_READY.set(1)


@app.on_event("shutdown")
async def _shutdown() -> None:  # noqa: D401
    """Clean up on shutdown."""
    APP_READY.set(0)
    logger.info("🛑 Noryon API shutdown completed.")


# ---------------------------------------------------------------------------
# Routes
# ---------------------------------------------------------------------------


@app.get("/healthz", response_class=JSONResponse, status_code=200, tags=["system"])
async def healthz() -> Dict[str, Any]:
    """Liveness / readiness probe used by Kubernetes & human checks."""
    return {"status": "ok"}


@app.get("/metrics", status_code=200, tags=["system"])
async def metrics() -> Response:
    """Prometheus scrape endpoint."""
    # FastAPI will set content-type automatically if we pass custom headers
    return PlainTextResponse(generate_latest(), media_type=CONTENT_TYPE_LATEST) 