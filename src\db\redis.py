"""Redis asynchronous client utilities.

This helper centralises creation of a singleton ``redis.asyncio.Redis`` client,
mirroring the Postgres and ClickHouse helper modules created for Sprint-0.
It purposefully keeps a *very* small surface area for now:
    • ``get_client()`` – returns a shared asyncio Redis client instance.
    • ``ping()`` – lightweight connectivity check returning ``True``/``False``.

Reference: https://redis.readthedocs.io/en/stable/examples/asyncio_examples.html
"""

from __future__ import annotations

import logging
from typing import Final

import redis.asyncio as aioredis

from src.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

# Internal cached client (created lazily on first access)
_client: aioredis.Redis | None = None

# Timeout (in seconds) for the connectivity check – we keep it small because
# this runs inside unit tests / health-checks where long waits are undesirable.
PING_TIMEOUT: Final[int] = 2


def get_client() -> aioredis.Redis:
    """Return a *singleton* asyncio Redis client.

    The client is created lazily so importing this module never initiates a
    network connection – this keeps unit tests fast and deterministic.
    """
    global _client  # noqa: PLW0603 – we purposely cache the instance at module scope
    if _client is None:
        logger.info("Creating Redis client …")
        _client = aioredis.from_url(settings.redis_url, decode_responses=True)
    return _client


async def ping() -> bool:  # noqa: D401 – simple boolean return
    """Return ``True`` if Redis responds to *PING*, else ``False``.

    Any unexpected exception is caught and logged; callers can rely on the
    boolean result without needing their own ``try/except`` guards.
    """
    client = get_client()
    try:
        response = await client.ping()
        return response is True  # redis-py returns ``True``
    except Exception as exc:  # pylint: disable=broad-except
        logger.error("Redis ping failed: %s", exc, exc_info=settings.DEBUG)
        return False


async def close() -> None:  # pragma: no cover – rarely called in tests
    """Explicitly close the Redis connection (mainly for graceful shutdown)."""
    global _client  # noqa: PLW0603
    if _client is not None:
        await _client.close()
        _client = None