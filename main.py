#!/usr/bin/env python3
"""
Noryon V2 - Advanced Crypto Trading AI System
Main application entry point with 9 specialized AI agents
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime
from typing import Dict, List, Optional

from src.core.config import Config
from src.core.logger import setup_logging
from src.agents.market_watcher import MarketWatcherAgent
from src.agents.news_analyst import NewsAnalystAgent
from src.agents.technical_analyst import TechnicalAnalystAgent
from src.agents.chief_analyst import ChiefA<PERSON>ystAgent
from src.agents.researcher import ResearcherAgent
from src.agents.risk_manager import RiskManagerAgent
from src.agents.trader import TraderAgent
from src.agents.portfolio_manager import PortfolioManagerAgent
from src.agents.auditor import AuditorAgent
from src.core.orchestrator import SystemOrchestrator
from src.db.database_manager import DatabaseManager
from src.exchanges.exchange_manager import ExchangeManager
from src.monitoring.system_monitor import SystemMonitor
from src.api.api_server import APIServer

class NoryonTradingSystem:
    """Main trading system class that coordinates all components"""
    
    def __init__(self):
        self.config = Config()
        self.logger = logging.getLogger(__name__)
        self.running = False
        self.agents: Dict[str, object] = {}
        self.orchestrator: Optional[SystemOrchestrator] = None
        self.db_manager: Optional[DatabaseManager] = None
        self.exchange_manager: Optional[ExchangeManager] = None
        self.system_monitor: Optional[SystemMonitor] = None
        self.api_server: Optional[APIServer] = None
        
    async def initialize(self):
        """Initialize all system components"""
        try:
            self.logger.info("🚀 Initializing Noryon V2 Trading System...")
            
            # Initialize database connections
            self.db_manager = DatabaseManager(self.config)
            await self.db_manager.initialize()
            self.logger.info("✅ Database connections established")
            
            # Initialize exchange connections
            self.exchange_manager = ExchangeManager(self.config)
            await self.exchange_manager.initialize()
            self.logger.info("✅ Exchange connections established")
            
            # Initialize system monitoring
            self.system_monitor = SystemMonitor(self.config)
            await self.system_monitor.start()
            self.logger.info("✅ System monitoring started")
            
            # Initialize all AI agents
            await self._initialize_agents()
            self.logger.info("✅ All AI agents initialized")
            
            # Initialize system orchestrator
            self.orchestrator = SystemOrchestrator(
                agents=self.agents,
                db_manager=self.db_manager,
                exchange_manager=self.exchange_manager,
                config=self.config
            )
            await self.orchestrator.initialize()
            self.logger.info("✅ System orchestrator initialized")
            
            # Initialize API server
            self.api_server = APIServer(
                orchestrator=self.orchestrator,
                config=self.config
            )
            self.logger.info("✅ API server initialized")
            
            self.logger.info("🎯 Noryon V2 Trading System fully initialized!")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize system: {e}")
            raise
    
    async def _initialize_agents(self):
        """Initialize all 9 AI trading agents with their assigned models"""
        
        agent_configs = [
            {
                'name': 'market_watcher',
                'class': MarketWatcherAgent,
                'model': 'magistral:24b',
                'description': 'Real-time market monitoring and price tracking'
            },
            {
                'name': 'news_analyst',
                'class': NewsAnalystAgent,
                'model': 'command-r:35b',
                'description': 'News sentiment analysis and market impact assessment'
            },
            {
                'name': 'technical_analyst',
                'class': TechnicalAnalystAgent,
                'model': 'cogito:32b',
                'description': 'Technical analysis and chart pattern recognition'
            },
            {
                'name': 'chief_analyst',
                'class': ChiefAnalystAgent,
                'model': 'gemma3:27b',
                'description': 'Strategic decision making and trade coordination'
            },
            {
                'name': 'researcher',
                'class': ResearcherAgent,
                'model': 'mistral-small:24b',
                'description': 'Deep market research and fundamental analysis'
            },
            {
                'name': 'risk_manager',
                'class': RiskManagerAgent,
                'model': 'falcon3:10b',
                'description': 'Risk assessment and position sizing'
            },
            {
                'name': 'trader',
                'class': TraderAgent,
                'model': 'granite3.3:8b',
                'description': 'Trade execution and order management'
            },
            {
                'name': 'portfolio_manager',
                'class': PortfolioManagerAgent,
                'model': 'qwen3:32b',
                'description': 'Portfolio optimization and asset allocation'
            },
            {
                'name': 'auditor',
                'class': AuditorAgent,
                'model': 'deepseek-r1:latest',
                'description': 'Performance monitoring and compliance checking'
            }
        ]
        
        for agent_config in agent_configs:
            try:
                self.logger.info(f"🤖 Initializing {agent_config['name']} with {agent_config['model']}")
                
                agent = agent_config['class'](
                    model_name=agent_config['model'],
                    config=self.config,
                    db_manager=self.db_manager
                )
                
                await agent.initialize()
                self.agents[agent_config['name']] = agent
                
                self.logger.info(f"✅ {agent_config['name']} initialized: {agent_config['description']}")
                
            except Exception as e:
                self.logger.error(f"❌ Failed to initialize {agent_config['name']}: {e}")
                raise
    
    async def start(self):
        """Start the trading system"""
        try:
            self.running = True
            self.logger.info("🚀 Starting Noryon V2 Trading System...")
            
            # Start all agents
            for agent_name, agent in self.agents.items():
                await agent.start()
                self.logger.info(f"✅ {agent_name} started")
            
            # Start orchestrator
            await self.orchestrator.start()
            self.logger.info("✅ System orchestrator started")
            
            # Start API server
            await self.api_server.start()
            self.logger.info("✅ API server started on port 8000")
            
            self.logger.info("🎯 Noryon V2 Trading System is now LIVE!")
            self.logger.info("📊 Dashboard: http://localhost:8000/dashboard")
            self.logger.info("📡 API Docs: http://localhost:8000/docs")
            
            # Keep the system running
            while self.running:
                await asyncio.sleep(1)
                
        except Exception as e:
            self.logger.error(f"❌ Error starting system: {e}")
            await self.shutdown()
            raise
    
    async def shutdown(self):
        """Gracefully shutdown the trading system"""
        self.logger.info("🛑 Shutting down Noryon V2 Trading System...")
        self.running = False
        
        try:
            # Stop API server
            if self.api_server:
                await self.api_server.stop()
                self.logger.info("✅ API server stopped")
            
            # Stop orchestrator
            if self.orchestrator:
                await self.orchestrator.stop()
                self.logger.info("✅ System orchestrator stopped")
            
            # Stop all agents
            for agent_name, agent in self.agents.items():
                await agent.stop()
                self.logger.info(f"✅ {agent_name} stopped")
            
            # Stop system monitoring
            if self.system_monitor:
                await self.system_monitor.stop()
                self.logger.info("✅ System monitoring stopped")
            
            # Close exchange connections
            if self.exchange_manager:
                await self.exchange_manager.close()
                self.logger.info("✅ Exchange connections closed")
            
            # Close database connections
            if self.db_manager:
                await self.db_manager.close()
                self.logger.info("✅ Database connections closed")
            
            self.logger.info("✅ Noryon V2 Trading System shutdown complete")
            
        except Exception as e:
            self.logger.error(f"❌ Error during shutdown: {e}")

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    print("\n🛑 Received shutdown signal. Gracefully shutting down...")
    sys.exit(0)

async def main():
    """Main entry point"""
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create and run the trading system
    system = NoryonTradingSystem()
    
    try:
        await system.initialize()
        await system.start()
    except KeyboardInterrupt:
        logger.info("🛑 Received keyboard interrupt")
    except Exception as e:
        logger.error(f"❌ System error: {e}")
    finally:
        await system.shutdown()

if __name__ == "__main__":
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    NORYON V2 TRADING SYSTEM                  ║
    ║                  Advanced AI Crypto Trading                  ║
    ║                                                              ║
    ║  🤖 9 Specialized AI Agents                                  ║
    ║  📊 Real-time Market Analysis                                ║
    ║  ⚡ Automated Trading Strategies                             ║
    ║  🛡️ Advanced Risk Management                                 ║
    ║  📈 Portfolio Optimization                                   ║
    ║                                                              ║
    ║  Starting system initialization...                           ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    asyncio.run(main()) 