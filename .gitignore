# =============================================================================
# NORYON Crypto Trading Agents - Git Ignore File
# =============================================================================

# Environment Variables (CRITICAL - Contains API Keys)
.env
.env.local
.env.production
.env.staging
.env.development

# =============================================================================
# PYTHON
# =============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# =============================================================================
# TRADING & FINANCIAL DATA
# =============================================================================

# API Keys and Secrets (CRITICAL)
**/api_keys/
**/secrets/
**/credentials/
*.key
*.pem
*.p12
*.pfx

# Trading Data
data/raw/
data/processed/
data/backups/
data/exports/
*.csv
*.json
*.parquet
*.h5
*.hdf5

# Model Files
models/
*.pkl
*.pickle
*.joblib
*.model

# Backtest Results
backtests/
results/
reports/

# =============================================================================
# LOGS
# =============================================================================

# Log files
logs/
*.log
*.log.*
*.out
*.err

# =============================================================================
# DATABASES
# =============================================================================

# SQLite
*.db
*.sqlite
*.sqlite3

# Database dumps
*.sql
*.dump

# =============================================================================
# DOCKER & CONTAINERS
# =============================================================================

# Docker
.dockerignore
docker-compose.override.yml
docker-compose.local.yml

# =============================================================================
# IDE & EDITORS
# =============================================================================

# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# OPERATING SYSTEM
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# TEMPORARY FILES
# =============================================================================

# Temporary directories
tmp/
temp/
cache/
.cache/

# Backup files
*.bak
*.backup
*.old
*.orig

# =============================================================================
# CONFIGURATION FILES (Sensitive)
# =============================================================================

# Local configuration overrides
config/local/
config/production/
config/staging/

# SSL Certificates
*.crt
*.cert
*.ca-bundle

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================

# Prometheus data
prometheus_data/

# Grafana data
grafana_data/

# =============================================================================
# JUPYTER NOTEBOOKS
# =============================================================================

# Jupyter Notebook Checkpoints
.ipynb_checkpoints/

# Jupyter Lab
.jupyter/

# =============================================================================
# PACKAGE MANAGERS
# =============================================================================

# npm
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# =============================================================================
# CLOUD & DEPLOYMENT
# =============================================================================

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Kubernetes
*.kubeconfig

# AWS
.aws/

# =============================================================================
# TESTING
# =============================================================================

# Test outputs
test-results/
test-reports/
coverage-reports/

# =============================================================================
# CUSTOM PROJECT SPECIFIC
# =============================================================================

# Ollama models (too large for git)
ollama_models/

# Market data cache
market_data_cache/

# Strategy backtests
strategy_backtests/

# Performance reports
performance_reports/

# Risk reports
risk_reports/

# Trading history
trading_history/

# Portfolio snapshots
portfolio_snapshots/

# =============================================================================
# SECURITY SENSITIVE FILES
# =============================================================================

# Any file containing "secret", "key", "password", "token"
*secret*
*key*
*password*
*token*
*credential*

# Exclude specific patterns but allow certain files
!requirements.txt
!package.json
!setup.py
!Dockerfile*
!docker-compose.yml
!.gitignore
!README.md
!ARCHITECTURE.md
!LICENSE 