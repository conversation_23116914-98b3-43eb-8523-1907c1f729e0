# Noryon V2 - Crypto Trading AI System Requirements

# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Async Support
asyncio-mqtt==0.16.1
aiohttp==3.9.1
aiofiles==23.2.0
asyncpg==0.29.0
aiodns==3.1.1

# Database Drivers
asyncpg==0.29.0                    # PostgreSQL async driver
aioredis==2.0.1                    # Redis async driver
aioclickhouse==0.0.7               # ClickHouse async driver
motor==3.3.2                       # MongoDB async driver
sqlalchemy[asyncio]==2.0.23        # SQLAlchemy with async support

# Database Tools
alembic==1.13.1                    # Database migrations
psycopg2-binary==2.9.9             # PostgreSQL adapter
redis==5.0.1                       # Redis client
clickhouse-driver==0.2.6           # ClickHouse driver
pymongo==4.6.0                     # MongoDB driver

# Cryptocurrency Exchange APIs
ccxt==4.4.89                       # Unified exchange API (latest available)
python-binance==1.0.19             # Binance API
# coinbase-pro==1.1.4                # Coinbase Pro API (deprecated)
krakenex==2.1.0                    # Kraken API
pybit==5.6.0                       # Bybit API

# WebSocket Support
websockets==12.0                   # WebSocket client/server
python-socketio==5.10.0            # Socket.IO support
websocket-client==1.6.4            # WebSocket client

# Data Analysis & ML
numpy==1.25.2                      # Numerical computing
pandas==2.1.4                      # Data manipulation
scipy==1.11.4                      # Scientific computing
scikit-learn==1.3.2                # Machine learning
ta==0.10.2                         # Technical analysis
# TA-Lib==0.4.28                     # Technical analysis library (install separately)
matplotlib==3.8.2                  # Plotting
plotly==5.17.0                     # Interactive plots
seaborn==0.13.0                    # Statistical visualization

# Financial Data
yfinance==0.2.28                   # Yahoo Finance data
alpha-vantage==2.3.1               # Alpha Vantage API
quandl==3.7.0                      # Quandl financial data
fredapi==0.5.1                     # FRED economic data

# News & Sentiment Analysis
newsapi-python==0.2.7              # News API
textblob==0.17.1                   # Sentiment analysis
vaderSentiment==3.3.2              # VADER sentiment
transformers==4.36.2               # Hugging Face transformers
torch==2.1.2                       # PyTorch for ML models

# HTTP Clients & APIs
requests==2.31.0                   # HTTP library
httpx==0.25.2                      # Async HTTP client
urllib3==2.1.0                     # HTTP client

# Authentication & Security
python-jose[cryptography]==3.3.0   # JWT tokens
passlib[bcrypt]==1.7.4             # Password hashing
cryptography==41.0.8               # Cryptographic recipes
python-multipart==0.0.6            # Form data parsing

# Configuration & Environment
python-dotenv==1.0.0               # Environment variables
dynaconf==3.2.4                    # Configuration management
click==8.1.7                       # Command line interface

# Logging & Monitoring
loguru==0.7.2                      # Advanced logging
structlog==23.2.0                  # Structured logging
prometheus-client==0.19.0          # Prometheus metrics
sentry-sdk==1.39.2                 # Error tracking

# Task Queue & Scheduling
celery==5.3.4                      # Distributed task queue
redis==5.0.1                       # Celery broker
flower==2.0.1                      # Celery monitoring
schedule==1.2.0                    # Job scheduling
apscheduler==3.10.4                # Advanced scheduler

# Data Validation & Serialization
marshmallow==3.20.1                # Serialization
cerberus==1.3.5                    # Data validation
jsonschema==4.20.0                 # JSON schema validation

# Utilities
python-dateutil==2.8.2             # Date utilities
pytz==2023.3                       # Timezone handling
arrow==1.3.0                       # Date/time library
pendulum==2.1.2                    # Date/time library
humanize==4.8.0                    # Human readable values

# Development & Testing
pytest==7.4.3                      # Testing framework
pytest-asyncio==0.21.1             # Async testing
pytest-cov==4.1.0                  # Coverage testing
pytest-mock==3.12.0                # Mocking
black==23.11.0                     # Code formatting
flake8==6.1.0                      # Linting
mypy==1.7.1                        # Type checking
pre-commit==3.6.0                  # Git hooks

# Documentation
sphinx==7.2.6                      # Documentation generator
sphinx-rtd-theme==1.3.0            # ReadTheDocs theme

# Performance & Optimization
cython==3.0.6                      # C extensions
numba==0.58.1                      # JIT compilation
uvloop==0.19.0                     # Fast event loop (Unix only)

# Notification Services
discord.py==2.3.2                  # Discord notifications
python-telegram-bot==20.7          # Telegram notifications
slack-sdk==3.26.1                  # Slack notifications
sendgrid==6.11.0                   # Email notifications

# File Processing
openpyxl==3.1.2                    # Excel files
xlsxwriter==3.1.9                  # Excel writing
python-docx==1.1.0                 # Word documents
pypdf2==3.0.1                      # PDF processing

# Image Processing (for charts)
pillow==10.1.0                     # Image processing
opencv-python==4.8.1.78            # Computer vision

# Backup & Storage
boto3==1.34.0                      # AWS SDK
google-cloud-storage==2.10.0       # Google Cloud Storage
azure-storage-blob==12.19.0        # Azure Blob Storage

# System Monitoring
psutil==5.9.6                      # System utilities
py-cpuinfo==9.0.0                  # CPU information
GPUtil==1.4.0                      # GPU monitoring

# Rich Terminal Output
rich==13.7.0                       # Rich text and beautiful formatting
colorama==0.4.6                    # Cross-platform colored terminal text
tqdm==4.66.1                       # Progress bars

# API Documentation
fastapi-users==12.1.2              # User management
fastapi-limiter==0.1.5             # Rate limiting
fastapi-cache2==0.2.1              # Caching

# Containerization Support
gunicorn==21.2.0                   # WSGI HTTP Server
supervisor==4.2.5                  # Process control

# Additional Utilities
retrying==1.3.4                    # Retry decorator
tenacity==8.2.3                    # Retry library
cachetools==5.3.2                  # Caching utilities
python-magic==0.4.27               # File type detection

# Development Tools
ipython==8.18.1                    # Enhanced Python shell
jupyter==1.0.0                     # Jupyter notebooks
notebook==7.0.6                    # Jupyter notebook server

# Version Pinning for Stability
setuptools==69.0.2
wheel==0.42.0
pip==23.3.1 