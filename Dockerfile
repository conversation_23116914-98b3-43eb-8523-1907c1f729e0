FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements
COPY requirements-sprint1.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements-sprint1.txt

# Copy source code
COPY src/ /app/src/

# Set Python path
ENV PYTHONPATH=/app

# Default command (override in docker-compose)
CMD ["python", "-m", "src.api.main"] 