"""
Noryon V2 - Advanced Logging System
Comprehensive logging with structured output, performance monitoring, and multiple handlers
"""

import logging
import logging.handlers
import sys
import json
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
import asyncio
from contextlib import contextmanager
import time

class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured JSON logging"""
    
    def format(self, record: logging.LogRecord) -> str:
        # Create base log entry
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "thread": record.thread,
            "process": record.process
        }
        
        # Add exception information if present
        if record.exc_info:
            log_entry["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": traceback.format_exception(*record.exc_info)
            }
        
        # Add extra fields from the log record
        extra_fields = {}
        for key, value in record.__dict__.items():
            if key not in [
                'name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 'filename',
                'module', 'exc_info', 'exc_text', 'stack_info', 'lineno', 'funcName',
                'created', 'msecs', 'relativeCreated', 'thread', 'threadName',
                'processName', 'process', 'getMessage', 'message'
            ]:
                extra_fields[key] = value
        
        if extra_fields:
            log_entry["extra"] = extra_fields
        
        return json.dumps(log_entry, default=str, ensure_ascii=False)

class ColoredConsoleFormatter(logging.Formatter):
    """Colored console formatter for better readability"""
    
    COLORS = {
        'DEBUG': '\033[36m',      # Cyan
        'INFO': '\033[32m',       # Green
        'WARNING': '\033[33m',    # Yellow
        'ERROR': '\033[31m',      # Red
        'CRITICAL': '\033[35m',   # Magenta
        'RESET': '\033[0m'        # Reset
    }
    
    def format(self, record: logging.LogRecord) -> str:
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset = self.COLORS['RESET']
        
        # Format timestamp
        timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S')
        
        # Format the log message
        formatted_message = (
            f"{color}[{timestamp}] {record.levelname:8s}{reset} "
            f"| {record.name:20s} | {record.funcName:15s}:{record.lineno:4d} "
            f"| {record.getMessage()}"
        )
        
        # Add exception information if present
        if record.exc_info:
            formatted_message += f"\n{self.formatException(record.exc_info)}"
        
        return formatted_message

class PerformanceLogger:
    """Logger for performance monitoring and metrics"""
    
    def __init__(self, logger_name: str = "performance"):
        self.logger = logging.getLogger(logger_name)
        self.metrics: Dict[str, Any] = {}
    
    @contextmanager
    def timer(self, operation_name: str, **kwargs):
        """Context manager for timing operations"""
        start_time = time.time()
        start_timestamp = datetime.utcnow()
        
        try:
            yield
            success = True
            error = None
        except Exception as e:
            success = False
            error = str(e)
            raise
        finally:
            end_time = time.time()
            duration = end_time - start_time
            
            self.log_performance(
                operation=operation_name,
                duration=duration,
                success=success,
                error=error,
                start_time=start_timestamp,
                **kwargs
            )
    
    def log_performance(self, operation: str, duration: float, success: bool = True, 
                       error: Optional[str] = None, **kwargs):
        """Log performance metrics"""
        self.logger.info(
            f"Performance metric: {operation}",
            extra={
                "metric_type": "performance",
                "operation": operation,
                "duration_seconds": duration,
                "duration_ms": duration * 1000,
                "success": success,
                "error": error,
                **kwargs
            }
        )
        
        # Store in metrics for aggregation
        if operation not in self.metrics:
            self.metrics[operation] = {
                "count": 0,
                "total_duration": 0,
                "success_count": 0,
                "error_count": 0,
                "min_duration": float('inf'),
                "max_duration": 0
            }
        
        metric = self.metrics[operation]
        metric["count"] += 1
        metric["total_duration"] += duration
        metric["min_duration"] = min(metric["min_duration"], duration)
        metric["max_duration"] = max(metric["max_duration"], duration)
        
        if success:
            metric["success_count"] += 1
        else:
            metric["error_count"] += 1
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get aggregated performance metrics"""
        summary = {}
        for operation, metric in self.metrics.items():
            if metric["count"] > 0:
                summary[operation] = {
                    "count": metric["count"],
                    "success_rate": metric["success_count"] / metric["count"],
                    "error_rate": metric["error_count"] / metric["count"],
                    "avg_duration": metric["total_duration"] / metric["count"],
                    "min_duration": metric["min_duration"],
                    "max_duration": metric["max_duration"],
                    "total_duration": metric["total_duration"]
                }
        return summary

class TradingLogger:
    """Specialized logger for trading activities"""
    
    def __init__(self, logger_name: str = "trading"):
        self.logger = logging.getLogger(logger_name)
    
    def log_trade(self, action: str, symbol: str, quantity: float, price: float, 
                  exchange: str, strategy: str, **kwargs):
        """Log trading activities"""
        self.logger.info(
            f"Trade {action}: {quantity} {symbol} at {price} on {exchange}",
            extra={
                "event_type": "trade",
                "action": action,
                "symbol": symbol,
                "quantity": quantity,
                "price": price,
                "exchange": exchange,
                "strategy": strategy,
                "value": quantity * price,
                **kwargs
            }
        )
    
    def log_order(self, order_type: str, symbol: str, quantity: float, price: float,
                  order_id: str, status: str, **kwargs):
        """Log order activities"""
        self.logger.info(
            f"Order {order_type}: {order_id} - {quantity} {symbol} at {price} ({status})",
            extra={
                "event_type": "order",
                "order_type": order_type,
                "symbol": symbol,
                "quantity": quantity,
                "price": price,
                "order_id": order_id,
                "status": status,
                **kwargs
            }
        )
    
    def log_signal(self, signal_type: str, symbol: str, strength: float, 
                   source: str, **kwargs):
        """Log trading signals"""
        self.logger.info(
            f"Signal {signal_type}: {symbol} (strength: {strength}) from {source}",
            extra={
                "event_type": "signal",
                "signal_type": signal_type,
                "symbol": symbol,
                "strength": strength,
                "source": source,
                **kwargs
            }
        )
    
    def log_portfolio_update(self, total_value: float, pnl: float, 
                           positions: Dict[str, Any], **kwargs):
        """Log portfolio updates"""
        self.logger.info(
            f"Portfolio update: Total value {total_value}, PnL {pnl}",
            extra={
                "event_type": "portfolio",
                "total_value": total_value,
                "pnl": pnl,
                "positions": positions,
                "position_count": len(positions),
                **kwargs
            }
        )

class AgentLogger:
    """Specialized logger for AI agent activities"""
    
    def __init__(self, agent_name: str):
        self.agent_name = agent_name
        self.logger = logging.getLogger(f"agent.{agent_name}")
    
    def log_analysis(self, analysis_type: str, symbol: str, result: Dict[str, Any], 
                    confidence: float, **kwargs):
        """Log AI analysis results"""
        self.logger.info(
            f"Analysis {analysis_type}: {symbol} (confidence: {confidence})",
            extra={
                "event_type": "analysis",
                "agent": self.agent_name,
                "analysis_type": analysis_type,
                "symbol": symbol,
                "result": result,
                "confidence": confidence,
                **kwargs
            }
        )
    
    def log_decision(self, decision_type: str, decision: str, reasoning: str, 
                    confidence: float, **kwargs):
        """Log AI decision making"""
        self.logger.info(
            f"Decision {decision_type}: {decision}",
            extra={
                "event_type": "decision",
                "agent": self.agent_name,
                "decision_type": decision_type,
                "decision": decision,
                "reasoning": reasoning,
                "confidence": confidence,
                **kwargs
            }
        )
    
    def log_communication(self, target_agent: str, message_type: str, 
                         message: str, **kwargs):
        """Log inter-agent communication"""
        self.logger.info(
            f"Communication to {target_agent}: {message_type}",
            extra={
                "event_type": "communication",
                "source_agent": self.agent_name,
                "target_agent": target_agent,
                "message_type": message_type,
                "message": message,
                **kwargs
            }
        )

def setup_logging(log_level: str = "INFO", log_dir: str = "logs", 
                 enable_console: bool = True, enable_file: bool = True,
                 enable_json: bool = True) -> None:
    """Setup comprehensive logging system"""
    
    # Create logs directory
    log_path = Path(log_dir)
    log_path.mkdir(exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler with colored output
    if enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(ColoredConsoleFormatter())
        root_logger.addHandler(console_handler)
    
    # File handler for general logs
    if enable_file:
        file_handler = logging.handlers.RotatingFileHandler(
            log_path / "noryon.log",
            maxBytes=50 * 1024 * 1024,  # 50MB
            backupCount=10,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(
            logging.Formatter(
                '%(asctime)s | %(levelname)-8s | %(name)-20s | %(funcName)-15s:%(lineno)-4d | %(message)s'
            )
        )
        root_logger.addHandler(file_handler)
    
    # JSON structured logging for analysis
    if enable_json:
        json_handler = logging.handlers.RotatingFileHandler(
            log_path / "noryon_structured.jsonl",
            maxBytes=100 * 1024 * 1024,  # 100MB
            backupCount=5,
            encoding='utf-8'
        )
        json_handler.setLevel(logging.INFO)
        json_handler.setFormatter(StructuredFormatter())
        root_logger.addHandler(json_handler)
    
    # Separate handlers for different log types
    
    # Trading logs
    trading_handler = logging.handlers.RotatingFileHandler(
        log_path / "trading.log",
        maxBytes=50 * 1024 * 1024,
        backupCount=20,
        encoding='utf-8'
    )
    trading_handler.setLevel(logging.INFO)
    trading_handler.setFormatter(StructuredFormatter())
    trading_handler.addFilter(lambda record: record.name.startswith('trading'))
    root_logger.addHandler(trading_handler)
    
    # Performance logs
    performance_handler = logging.handlers.RotatingFileHandler(
        log_path / "performance.log",
        maxBytes=25 * 1024 * 1024,
        backupCount=10,
        encoding='utf-8'
    )
    performance_handler.setLevel(logging.INFO)
    performance_handler.setFormatter(StructuredFormatter())
    performance_handler.addFilter(lambda record: record.name.startswith('performance'))
    root_logger.addHandler(performance_handler)
    
    # Agent logs
    agent_handler = logging.handlers.RotatingFileHandler(
        log_path / "agents.log",
        maxBytes=50 * 1024 * 1024,
        backupCount=15,
        encoding='utf-8'
    )
    agent_handler.setLevel(logging.INFO)
    agent_handler.setFormatter(StructuredFormatter())
    agent_handler.addFilter(lambda record: record.name.startswith('agent'))
    root_logger.addHandler(agent_handler)
    
    # Error logs
    error_handler = logging.handlers.RotatingFileHandler(
        log_path / "errors.log",
        maxBytes=25 * 1024 * 1024,
        backupCount=10,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(StructuredFormatter())
    root_logger.addHandler(error_handler)
    
    # Configure specific loggers
    
    # Reduce noise from external libraries
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)
    logging.getLogger("aiohttp").setLevel(logging.WARNING)
    logging.getLogger("websockets").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    
    # Log startup message
    logger = logging.getLogger(__name__)
    logger.info("Noryon V2 logging system initialized", extra={
        "log_level": log_level,
        "log_dir": str(log_path),
        "console_enabled": enable_console,
        "file_enabled": enable_file,
        "json_enabled": enable_json
    })

def get_logger(name: str) -> logging.Logger:
    """Get a logger instance"""
    return logging.getLogger(name)

def get_performance_logger(name: str = "performance") -> PerformanceLogger:
    """Get a performance logger instance"""
    return PerformanceLogger(name)

def get_trading_logger(name: str = "trading") -> TradingLogger:
    """Get a trading logger instance"""
    return TradingLogger(name)

def get_agent_logger(agent_name: str) -> AgentLogger:
    """Get an agent logger instance"""
    return AgentLogger(agent_name) 