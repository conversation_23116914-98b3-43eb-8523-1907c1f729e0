#!/usr/bin/env python3
"""
Noryon V2 - Quick Setup Script
Simplified setup for immediate deployment
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_step(message):
    print(f"{Colors.OKCYAN}🔧 {message}{Colors.ENDC}")

def print_success(message):
    print(f"{Colors.OKGREEN}✅ {message}{Colors.ENDC}")

def print_warning(message):
    print(f"{Colors.WARNING}⚠️  {message}{Colors.ENDC}")

def print_error(message):
    print(f"{Colors.FAIL}❌ {message}{Colors.ENDC}")

def run_command(command, description):
    """Run a command and handle errors"""
    print_step(f"{description}...")
    try:
        if platform.system() == "Windows":
            result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        else:
            result = subprocess.run(command.split(), check=True, capture_output=True, text=True)
        print_success(f"{description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print_error(f"{description} failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def main():
    print(f"""
{Colors.HEADER}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════════════════════╗
║                           NORYON V2 QUICK SETUP                             ║
║                      Advanced AI Crypto Trading System                      ║
╚══════════════════════════════════════════════════════════════════════════════╝
{Colors.ENDC}
    """)
    
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    print(f"System: {platform.system()}")
    print(f"Python: {platform.python_version()}")
    print(f"Working Directory: {os.getcwd()}")
    
    # Step 1: Create virtual environment
    venv_path = project_root / "venv"
    if not venv_path.exists():
        if not run_command(f"{sys.executable} -m venv venv", "Creating virtual environment"):
            return False
    else:
        print_success("Virtual environment already exists")
    
    # Step 2: Activate virtual environment and upgrade pip
    if platform.system() == "Windows":
        pip_cmd = "venv\\Scripts\\pip"
        python_cmd = "venv\\Scripts\\python"
    else:
        pip_cmd = "venv/bin/pip"
        python_cmd = "venv/bin/python"
    
    # Upgrade pip first
    if not run_command(f"{pip_cmd} install --upgrade pip", "Upgrading pip"):
        print_warning("Pip upgrade failed, continuing anyway...")
    
    # Step 3: Install core dependencies first
    core_deps = [
        "fastapi==0.104.1",
        "uvicorn[standard]==0.24.0",
        "pydantic==2.5.0",
        "pydantic-settings==2.1.0",
        "aiohttp==3.9.1",
        "asyncpg==0.29.0",
        "aioredis==2.0.1",
        "sqlalchemy[asyncio]==2.0.23",
        "ccxt==4.4.89",
        "python-binance==1.0.19",
        "websockets==12.0",
        "numpy==1.25.2",
        "pandas==2.1.4",
        "requests==2.31.0",
        "python-dotenv==1.0.0",
        "loguru==0.7.2"
    ]
    
    print_step("Installing core dependencies...")
    for dep in core_deps:
        if not run_command(f"{pip_cmd} install {dep}", f"Installing {dep.split('==')[0]}"):
            print_warning(f"Failed to install {dep}, continuing...")
    
    # Step 4: Install remaining dependencies (skip problematic ones)
    print_step("Installing remaining dependencies from requirements.txt...")
    
    # Read requirements and filter out problematic packages
    skip_packages = [
        "TA-Lib",  # Requires separate installation
        "coinbase-pro",  # Deprecated
        "uvloop",  # Unix only
        "aioclickhouse==0.1.6"  # Version not available
    ]
    
    try:
        with open("requirements.txt", "r") as f:
            requirements = f.readlines()
        
        for line in requirements:
            line = line.strip()
            if line and not line.startswith("#") and not any(skip in line for skip in skip_packages):
                package = line.split("==")[0].split(">=")[0].split("<=")[0]
                if package not in [dep.split("==")[0] for dep in core_deps]:
                    run_command(f"{pip_cmd} install {line}", f"Installing {package}")
    
    except Exception as e:
        print_warning(f"Error reading requirements.txt: {e}")
    
    # Step 5: Create environment file
    print_step("Creating environment configuration...")
    if not Path(".env").exists():
        try:
            with open("env.example", "r") as src, open(".env", "w") as dst:
                dst.write(src.read())
            print_success("Created .env file from template")
        except FileNotFoundError:
            print_warning("env.example not found, creating basic .env file")
            with open(".env", "w") as f:
                f.write("""# Noryon V2 Configuration
DEBUG=true
LOG_LEVEL=INFO
DATABASE_URL=postgresql://noryon:password@localhost:5432/noryon_trading
REDIS_URL=redis://localhost:6379/0

# Exchange API Keys (Add your keys here)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
COINBASE_API_KEY=your_coinbase_api_key_here
COINBASE_SECRET_KEY=your_coinbase_secret_key_here

# Ollama Configuration
OLLAMA_HOST=http://localhost:11434
""")
    
    # Step 6: Test the installation
    print_step("Testing installation...")
    test_script = """
import sys
try:
    import fastapi
    import uvicorn
    import pydantic
    import aiohttp
    import asyncpg
    import ccxt
    import pandas
    import numpy
    print("✅ Core dependencies imported successfully")
    sys.exit(0)
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
"""
    
    with open("test_imports.py", "w") as f:
        f.write(test_script)
    
    if run_command(f"{python_cmd} test_imports.py", "Testing core imports"):
        os.remove("test_imports.py")
        print_success("Installation test passed!")
    else:
        print_error("Installation test failed")
        return False
    
    # Step 7: Display next steps
    print(f"""
{Colors.OKGREEN}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════════════════════╗
║                              SETUP COMPLETE!                                ║
╚══════════════════════════════════════════════════════════════════════════════╝
{Colors.ENDC}

{Colors.OKCYAN}Next Steps:{Colors.ENDC}

1. {Colors.OKBLUE}Configure your .env file:{Colors.ENDC}
   - Add your exchange API keys
   - Configure database connections
   - Set up notification services

2. {Colors.OKBLUE}Install Ollama models:{Colors.ENDC}
   - ollama pull magistral:24b
   - ollama pull command-r:35b
   - ollama pull gemma3:27b
   - ollama pull qwen3:32b

3. {Colors.OKBLUE}Start the databases:{Colors.ENDC}
   - docker-compose up -d postgres redis

4. {Colors.OKBLUE}Run the system:{Colors.ENDC}
   - {python_cmd} main.py

5. {Colors.OKBLUE}Access the web interface:{Colors.ENDC}
   - http://localhost:8000

{Colors.WARNING}Important Notes:{Colors.ENDC}
- TA-Lib requires separate installation for advanced technical analysis
- Some packages may need manual installation if they failed
- Configure your exchange API keys before trading
- Start with paper trading mode for testing

{Colors.OKGREEN}Happy Trading! 🚀{Colors.ENDC}
    """)
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1) 