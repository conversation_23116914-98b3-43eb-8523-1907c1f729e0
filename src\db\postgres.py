"""PostgreSQL asynchronous database utilities.

This module is intentionally minimal for Sprint-0. It exposes:
  • async_engine – shared AsyncEngine instance.
  • async_session_factory() – context-managed session generator.

SQLAlchemy 2.0 style async engine is used, referencing official docs:
https://docs.sqlalchemy.org/en/20/orm/extensions/asyncio.html
"""

from __future__ import annotations

import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession, async_sessionmaker, create_async_engine

from src.core.config import Settings

logger = logging.getLogger(__name__)
settings = Settings()  # Loads env vars via pydantic BaseSettings

# ---------------------------------------------------------------------------
# Engine creation
# ---------------------------------------------------------------------------

DATABASE_URL_ASYNC = settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://", 1)

async_engine: AsyncEngine = create_async_engine(
    DATABASE_URL_ASYNC,
    echo=settings.DEBUG,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
)

# Session factory (2.0)
async_session_factory = async_sessionmaker(async_engine, expire_on_commit=False)


@asynccontextmanager
async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """Yield an AsyncSession, ensuring proper cleanup."""
    async with async_session_factory() as session:
        try:
            yield session
        finally:
            # SQLAlchemy handles rollback on close when expire_on_commit=False.
            await session.close() 