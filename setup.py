#!/usr/bin/env python3
"""
Noryon V2 - Automated Setup Script
Comprehensive system initialization and configuration
"""

import os
import sys
import asyncio
import subprocess
import json
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional
import platform
import time
from datetime import datetime

# Color codes for terminal output
class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def print_banner():
    """Print the Noryon V2 banner"""
    banner = f"""
{Colors.HEADER}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════════════════════╗
║                           NORYON V2 SETUP SCRIPT                            ║
║                      Advanced AI Crypto Trading System                      ║
║                                                                              ║
║  🤖 9 Specialized AI Agents                                                  ║
║  📊 Real-time Market Analysis                                                ║
║  ⚡ Automated Trading Strategies                                             ║
║  🛡️ Advanced Risk Management                                                 ║
║  📈 Portfolio Optimization                                                   ║
║                                                                              ║
║  Starting automated setup and configuration...                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
{Colors.ENDC}
    """
    print(banner)

def print_step(step_num: int, total_steps: int, description: str):
    """Print setup step"""
    print(f"\n{Colors.OKCYAN}[{step_num}/{total_steps}] {description}{Colors.ENDC}")

def print_success(message: str):
    """Print success message"""
    print(f"{Colors.OKGREEN}✅ {message}{Colors.ENDC}")

def print_warning(message: str):
    """Print warning message"""
    print(f"{Colors.WARNING}⚠️  {message}{Colors.ENDC}")

def print_error(message: str):
    """Print error message"""
    print(f"{Colors.FAIL}❌ {message}{Colors.ENDC}")

def print_info(message: str):
    """Print info message"""
    print(f"{Colors.OKBLUE}ℹ️  {message}{Colors.ENDC}")

class NoryonSetup:
    """Main setup class for Noryon V2 trading system"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.system_info = self._get_system_info()
        self.setup_config = {}
        self.total_steps = 15
        
    def _get_system_info(self) -> Dict[str, Any]:
        """Get system information"""
        return {
            'platform': platform.system(),
            'architecture': platform.architecture()[0],
            'python_version': platform.python_version(),
            'cpu_count': os.cpu_count(),
            'cwd': os.getcwd()
        }
    
    async def run_setup(self):
        """Run the complete setup process"""
        try:
            print_banner()
            
            print_info(f"System: {self.system_info['platform']} {self.system_info['architecture']}")
            print_info(f"Python: {self.system_info['python_version']}")
            print_info(f"CPU Cores: {self.system_info['cpu_count']}")
            print_info(f"Project Root: {self.project_root}")
            
            # Setup steps
            await self._step_1_check_prerequisites()
            await self._step_2_create_directories()
            await self._step_3_setup_virtual_environment()
            await self._step_4_install_dependencies()
            await self._step_5_setup_databases()
            await self._step_6_configure_environment()
            await self._step_7_setup_ollama()
            await self._step_8_initialize_databases()
            await self._step_9_setup_logging()
            await self._step_10_configure_exchanges()
            await self._step_11_setup_monitoring()
            await self._step_12_create_systemd_services()
            await self._step_13_setup_backup_system()
            await self._step_14_run_tests()
            await self._step_15_final_configuration()
            
            await self._display_completion_summary()
            
        except Exception as e:
            print_error(f"Setup failed: {e}")
            sys.exit(1)
    
    async def _step_1_check_prerequisites(self):
        """Check system prerequisites"""
        print_step(1, self.total_steps, "Checking system prerequisites")
        
        # Check Python version
        if sys.version_info < (3, 9):
            raise Exception("Python 3.9 or higher is required")
        print_success("Python version check passed")
        
        # Check required system packages
        required_packages = ['git', 'curl', 'wget']
        for package in required_packages:
            if not shutil.which(package):
                print_warning(f"{package} not found, please install it manually")
            else:
                print_success(f"{package} found")
        
        # Check available disk space (minimum 10GB)
        disk_usage = shutil.disk_usage(self.project_root)
        free_gb = disk_usage.free / (1024**3)
        if free_gb < 10:
            print_warning(f"Low disk space: {free_gb:.1f}GB available (10GB recommended)")
        else:
            print_success(f"Disk space check passed: {free_gb:.1f}GB available")
        
        # Check memory (minimum 8GB recommended)
        try:
            import psutil
            memory_gb = psutil.virtual_memory().total / (1024**3)
            if memory_gb < 8:
                print_warning(f"Low memory: {memory_gb:.1f}GB (8GB recommended)")
            else:
                print_success(f"Memory check passed: {memory_gb:.1f}GB available")
        except ImportError:
            print_info("psutil not available, skipping memory check")
    
    async def _step_2_create_directories(self):
        """Create necessary directories"""
        print_step(2, self.total_steps, "Creating project directories")
        
        directories = [
            'logs',
            'data',
            'data/market_data',
            'data/backups',
            'data/exports',
            'config',
            'scripts',
            'tests',
            'docs',
            'src/agents',
            'src/core',
            'src/db',
            'src/exchanges',
            'src/strategies',
            'src/monitoring',
            'src/api',
            'src/utils',
            'src/web',
            'static',
            'templates',
            'migrations'
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            print_success(f"Created directory: {directory}")
    
    async def _step_3_setup_virtual_environment(self):
        """Setup Python virtual environment"""
        print_step(3, self.total_steps, "Setting up virtual environment")
        
        venv_path = self.project_root / 'venv'
        
        if not venv_path.exists():
            print_info("Creating virtual environment...")
            subprocess.run([sys.executable, '-m', 'venv', str(venv_path)], check=True)
            print_success("Virtual environment created")
        else:
            print_success("Virtual environment already exists")
        
        # Determine activation script path
        if self.system_info['platform'] == 'Windows':
            activate_script = venv_path / 'Scripts' / 'activate.bat'
            pip_path = venv_path / 'Scripts' / 'pip.exe'
        else:
            activate_script = venv_path / 'bin' / 'activate'
            pip_path = venv_path / 'bin' / 'pip'
        
        print_success(f"Virtual environment ready at: {venv_path}")
        print_info(f"Activation script: {activate_script}")
    
    async def _step_4_install_dependencies(self):
        """Install Python dependencies"""
        print_step(4, self.total_steps, "Installing Python dependencies")
        
        venv_path = self.project_root / 'venv'
        
        if self.system_info['platform'] == 'Windows':
            pip_path = venv_path / 'Scripts' / 'pip.exe'
        else:
            pip_path = venv_path / 'bin' / 'pip'
        
        if not pip_path.exists():
            print_error("Virtual environment pip not found")
            return
        
        # Upgrade pip first
        print_info("Upgrading pip...")
        subprocess.run([str(pip_path), 'install', '--upgrade', 'pip'], check=True)
        
        # Install requirements
        requirements_file = self.project_root / 'requirements.txt'
        if requirements_file.exists():
            print_info("Installing requirements...")
            subprocess.run([str(pip_path), 'install', '-r', str(requirements_file)], check=True)
            print_success("Dependencies installed successfully")
        else:
            print_warning("requirements.txt not found, skipping dependency installation")
    
    async def _step_5_setup_databases(self):
        """Setup database services"""
        print_step(5, self.total_steps, "Setting up database services")
        
        # Create docker-compose file if it doesn't exist
        docker_compose_path = self.project_root / 'docker-compose.yml'
        
        if not docker_compose_path.exists():
            await self._create_docker_compose()
        
        # Check if Docker is available
        if shutil.which('docker') and shutil.which('docker-compose'):
            print_info("Starting database services with Docker...")
            try:
                subprocess.run(['docker-compose', 'up', '-d'], 
                             cwd=self.project_root, check=True)
                print_success("Database services started")
                
                # Wait for services to be ready
                print_info("Waiting for services to be ready...")
                await asyncio.sleep(30)
                
            except subprocess.CalledProcessError:
                print_warning("Failed to start Docker services")
                print_info("Please start database services manually:")
                print_info("- PostgreSQL on port 5432")
                print_info("- Redis on port 6379")
                print_info("- ClickHouse on port 8123")
                print_info("- MongoDB on port 27017")
        else:
            print_warning("Docker not found, please install database services manually")
    
    async def _step_6_configure_environment(self):
        """Configure environment variables"""
        print_step(6, self.total_steps, "Configuring environment variables")
        
        env_file = self.project_root / '.env'
        env_example = self.project_root / '.env.example'
        
        if not env_file.exists():
            if env_example.exists():
                shutil.copy(env_example, env_file)
                print_success("Created .env file from template")
            else:
                await self._create_env_file()
                print_success("Created default .env file")
        else:
            print_success(".env file already exists")
        
        print_info("Please review and update the .env file with your API keys and settings")
    
    async def _step_7_setup_ollama(self):
        """Setup Ollama for AI models"""
        print_step(7, self.total_steps, "Setting up Ollama AI models")
        
        # Check if Ollama is installed
        if not shutil.which('ollama'):
            print_warning("Ollama not found. Please install Ollama manually:")
            print_info("Visit: https://ollama.ai/download")
            print_info("After installation, run: ollama pull <model_name> for each required model")
            return
        
        # Check if Ollama service is running
        try:
            subprocess.run(['ollama', 'list'], check=True, capture_output=True)
            print_success("Ollama is running")
        except subprocess.CalledProcessError:
            print_warning("Ollama service not running. Please start it manually:")
            print_info("Run: ollama serve")
            return
        
        # Pull required models
        required_models = [
            'magistral:24b',
            'command-r:35b',
            'cogito:32b',
            'gemma3:27b',
            'mistral-small:24b',
            'falcon3:10b',
            'granite3.3:8b',
            'qwen3:32b',
            'deepseek-r1:latest'
        ]
        
        print_info("Checking required AI models...")
        for model in required_models:
            try:
                result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
                if model in result.stdout:
                    print_success(f"Model {model} already available")
                else:
                    print_info(f"Pulling model {model}...")
                    subprocess.run(['ollama', 'pull', model], check=True)
                    print_success(f"Model {model} pulled successfully")
            except subprocess.CalledProcessError:
                print_warning(f"Failed to pull model {model}")
    
    async def _step_8_initialize_databases(self):
        """Initialize database schemas"""
        print_step(8, self.total_steps, "Initializing database schemas")
        
        # This would run the database initialization script
        print_info("Database schemas will be created on first run")
        print_success("Database initialization configured")
    
    async def _step_9_setup_logging(self):
        """Setup logging configuration"""
        print_step(9, self.total_steps, "Setting up logging system")
        
        logs_dir = self.project_root / 'logs'
        logs_dir.mkdir(exist_ok=True)
        
        # Create log files
        log_files = [
            'noryon.log',
            'trading.log',
            'agents.log',
            'performance.log',
            'errors.log'
        ]
        
        for log_file in log_files:
            log_path = logs_dir / log_file
            log_path.touch(exist_ok=True)
        
        print_success("Logging system configured")
    
    async def _step_10_configure_exchanges(self):
        """Configure exchange connections"""
        print_step(10, self.total_steps, "Configuring exchange connections")
        
        print_info("Exchange configuration will be loaded from .env file")
        print_info("Supported exchanges:")
        print_info("- Binance (set BINANCE_API_KEY and BINANCE_SECRET_KEY)")
        print_info("- Coinbase Pro (set COINBASE_API_KEY, COINBASE_SECRET_KEY, COINBASE_PASSPHRASE)")
        print_info("- Kraken (set KRAKEN_API_KEY and KRAKEN_SECRET_KEY)")
        print_info("- Bybit (set BYBIT_API_KEY and BYBIT_SECRET_KEY)")
        
        print_success("Exchange configuration ready")
    
    async def _step_11_setup_monitoring(self):
        """Setup monitoring and alerting"""
        print_step(11, self.total_steps, "Setting up monitoring and alerting")
        
        # Create monitoring configuration
        monitoring_config = {
            'metrics_enabled': True,
            'prometheus_port': 9090,
            'grafana_port': 3000,
            'alert_channels': {
                'discord': False,
                'telegram': False,
                'email': False,
                'slack': False
            }
        }
        
        config_path = self.project_root / 'config' / 'monitoring.json'
        with open(config_path, 'w') as f:
            json.dump(monitoring_config, f, indent=2)
        
        print_success("Monitoring configuration created")
    
    async def _step_12_create_systemd_services(self):
        """Create systemd services (Linux only)"""
        print_step(12, self.total_steps, "Creating system services")
        
        if self.system_info['platform'] != 'Linux':
            print_info("Systemd services only available on Linux")
            return
        
        # Create systemd service file
        service_content = f"""[Unit]
Description=Noryon V2 Crypto Trading System
After=network.target

[Service]
Type=simple
User={os.getenv('USER', 'noryon')}
WorkingDirectory={self.project_root}
Environment=PATH={self.project_root}/venv/bin
ExecStart={self.project_root}/venv/bin/python main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
        
        service_path = self.project_root / 'scripts' / 'noryon.service'
        with open(service_path, 'w') as f:
            f.write(service_content)
        
        print_success("Systemd service file created at scripts/noryon.service")
        print_info("To install: sudo cp scripts/noryon.service /etc/systemd/system/")
        print_info("To enable: sudo systemctl enable noryon")
        print_info("To start: sudo systemctl start noryon")
    
    async def _step_13_setup_backup_system(self):
        """Setup backup system"""
        print_step(13, self.total_steps, "Setting up backup system")
        
        # Create backup script
        backup_script = f"""#!/bin/bash
# Noryon V2 Backup Script

BACKUP_DIR="{self.project_root}/data/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="noryon_backup_$DATE"

echo "Starting backup: $BACKUP_NAME"

# Create backup directory
mkdir -p "$BACKUP_DIR/$BACKUP_NAME"

# Backup databases
echo "Backing up PostgreSQL..."
pg_dump noryon_trading > "$BACKUP_DIR/$BACKUP_NAME/postgres.sql"

echo "Backing up configuration..."
cp -r config "$BACKUP_DIR/$BACKUP_NAME/"
cp .env "$BACKUP_DIR/$BACKUP_NAME/" 2>/dev/null || true

echo "Backing up logs..."
cp -r logs "$BACKUP_DIR/$BACKUP_NAME/"

# Compress backup
echo "Compressing backup..."
cd "$BACKUP_DIR"
tar -czf "$BACKUP_NAME.tar.gz" "$BACKUP_NAME"
rm -rf "$BACKUP_NAME"

echo "Backup completed: $BACKUP_DIR/$BACKUP_NAME.tar.gz"

# Clean old backups (keep last 30 days)
find "$BACKUP_DIR" -name "noryon_backup_*.tar.gz" -mtime +30 -delete
"""
        
        backup_script_path = self.project_root / 'scripts' / 'backup.sh'
        with open(backup_script_path, 'w') as f:
            f.write(backup_script)
        
        # Make script executable
        if self.system_info['platform'] != 'Windows':
            os.chmod(backup_script_path, 0o755)
        
        print_success("Backup system configured")
    
    async def _step_14_run_tests(self):
        """Run system tests"""
        print_step(14, self.total_steps, "Running system tests")
        
        # Create a simple test to verify setup
        test_results = {
            'directories': True,
            'dependencies': True,
            'configuration': True
        }
        
        # Check directories
        required_dirs = ['src', 'logs', 'config', 'data']
        for directory in required_dirs:
            if not (self.project_root / directory).exists():
                test_results['directories'] = False
                break
        
        # Check configuration
        if not (self.project_root / '.env').exists():
            test_results['configuration'] = False
        
        # Display results
        for test, passed in test_results.items():
            if passed:
                print_success(f"{test.capitalize()} test passed")
            else:
                print_error(f"{test.capitalize()} test failed")
        
        if all(test_results.values()):
            print_success("All tests passed")
        else:
            print_warning("Some tests failed, please review the setup")
    
    async def _step_15_final_configuration(self):
        """Final configuration and setup completion"""
        print_step(15, self.total_steps, "Finalizing configuration")
        
        # Create startup script
        if self.system_info['platform'] == 'Windows':
            startup_script = f"""@echo off
cd /d "{self.project_root}"
call venv\\Scripts\\activate.bat
python main.py
pause
"""
            script_path = self.project_root / 'start_noryon.bat'
        else:
            startup_script = f"""#!/bin/bash
cd "{self.project_root}"
source venv/bin/activate
python main.py
"""
            script_path = self.project_root / 'start_noryon.sh'
            
        with open(script_path, 'w') as f:
            f.write(startup_script)
        
        if self.system_info['platform'] != 'Windows':
            os.chmod(script_path, 0o755)
        
        print_success("Startup script created")
        
        # Create configuration summary
        config_summary = {
            'setup_completed': datetime.utcnow().isoformat(),
            'system_info': self.system_info,
            'project_root': str(self.project_root),
            'python_executable': sys.executable,
            'setup_version': '2.0.0'
        }
        
        summary_path = self.project_root / 'config' / 'setup_summary.json'
        with open(summary_path, 'w') as f:
            json.dump(config_summary, f, indent=2)
        
        print_success("Setup configuration saved")
    
    async def _display_completion_summary(self):
        """Display setup completion summary"""
        print(f"\n{Colors.OKGREEN}{Colors.BOLD}")
        print("╔══════════════════════════════════════════════════════════════════════════════╗")
        print("║                        SETUP COMPLETED SUCCESSFULLY!                        ║")
        print("╚══════════════════════════════════════════════════════════════════════════════╝")
        print(f"{Colors.ENDC}")
        
        print(f"\n{Colors.OKCYAN}📋 Next Steps:{Colors.ENDC}")
        print("1. Review and update the .env file with your API keys")
        print("2. Ensure Ollama is running and models are downloaded")
        print("3. Start the database services (if not using Docker)")
        print("4. Run the system: python main.py")
        
        print(f"\n{Colors.OKCYAN}🚀 Quick Start:{Colors.ENDC}")
        if self.system_info['platform'] == 'Windows':
            print("   Double-click: start_noryon.bat")
        else:
            print("   Run: ./start_noryon.sh")
        
        print(f"\n{Colors.OKCYAN}📚 Documentation:{Colors.ENDC}")
        print("   README.md - Complete system documentation")
        print("   ARCHITECTURE.md - Technical architecture details")
        
        print(f"\n{Colors.OKCYAN}🔧 Configuration Files:{Colors.ENDC}")
        print("   .env - Environment variables and API keys")
        print("   config/ - System configuration files")
        
        print(f"\n{Colors.OKCYAN}📊 Monitoring:{Colors.ENDC}")
        print("   Dashboard: http://localhost:8000/dashboard")
        print("   API Docs: http://localhost:8000/docs")
        print("   Health Check: http://localhost:8000/health")
        
        print(f"\n{Colors.WARNING}⚠️  Important:{Colors.ENDC}")
        print("   - This system is for educational/research purposes")
        print("   - Start with paper trading mode")
        print("   - Never risk more than you can afford to lose")
        print("   - Always test thoroughly before live trading")
        
        print(f"\n{Colors.OKGREEN}✅ Setup completed at: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')} UTC{Colors.ENDC}")
    
    async def _create_docker_compose(self):
        """Create docker-compose.yml file"""
        docker_compose_content = """version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: noryon_postgres
    environment:
      POSTGRES_DB: noryon_trading
      POSTGRES_USER: noryon
      POSTGRES_PASSWORD: noryon123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: noryon_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  clickhouse:
    image: clickhouse/clickhouse-server:latest
    container_name: noryon_clickhouse
    ports:
      - "8123:8123"
      - "9000:9000"
    volumes:
      - clickhouse_data:/var/lib/clickhouse
    environment:
      CLICKHOUSE_DB: noryon_analytics
    restart: unless-stopped

  mongodb:
    image: mongo:7
    container_name: noryon_mongodb
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      MONGO_INITDB_DATABASE: noryon_logs
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  clickhouse_data:
  mongodb_data:
"""
        
        docker_compose_path = self.project_root / 'docker-compose.yml'
        with open(docker_compose_path, 'w') as f:
            f.write(docker_compose_content)
    
    async def _create_env_file(self):
        """Create default .env file"""
        env_content = """# Noryon V2 - Environment Configuration

# Environment Settings
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# API Server
API_HOST=0.0.0.0
API_PORT=8000

# Database Settings
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=noryon_trading
POSTGRES_USER=noryon
POSTGRES_PASSWORD=noryon123

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

CLICKHOUSE_HOST=localhost
CLICKHOUSE_PORT=8123
CLICKHOUSE_DB=noryon_analytics
CLICKHOUSE_USER=default
CLICKHOUSE_PASSWORD=

MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DB=noryon_logs

# Ollama Settings
OLLAMA_HOST=localhost
OLLAMA_PORT=11434
OLLAMA_TIMEOUT=300

# Exchange API Keys (REPLACE WITH YOUR ACTUAL KEYS)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
BINANCE_TESTNET=true

COINBASE_API_KEY=your_coinbase_api_key_here
COINBASE_SECRET_KEY=your_coinbase_secret_key_here
COINBASE_PASSPHRASE=your_coinbase_passphrase_here
COINBASE_SANDBOX=true

KRAKEN_API_KEY=your_kraken_api_key_here
KRAKEN_SECRET_KEY=your_kraken_secret_key_here

BYBIT_API_KEY=your_bybit_api_key_here
BYBIT_SECRET_KEY=your_bybit_secret_key_here
BYBIT_TESTNET=true

# Data Provider API Keys
COINGECKO_API_KEY=your_coingecko_api_key_here
COINMARKETCAP_API_KEY=your_coinmarketcap_api_key_here
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here
NEWSAPI_KEY=your_newsapi_key_here

# Trading Settings
PAPER_TRADING=true
INITIAL_BALANCE=10000
MAX_PORTFOLIO_RISK=0.02
MAX_POSITION_SIZE=0.1
MIN_TRADE_AMOUNT=10

# Risk Management
STOP_LOSS_PERCENTAGE=0.05
TAKE_PROFIT_PERCENTAGE=0.15
MAX_DRAWDOWN=0.1

# Notification Settings (Optional)
DISCORD_WEBHOOK_URL=
TELEGRAM_BOT_TOKEN=
TELEGRAM_CHAT_ID=
SLACK_WEBHOOK_URL=
"""
        
        env_path = self.project_root / '.env'
        with open(env_path, 'w') as f:
            f.write(env_content)

async def main():
    """Main setup function"""
    setup = NoryonSetup()
    await setup.run_setup()

if __name__ == "__main__":
    asyncio.run(main()) 