# Sprint-1: Market Data + Strategy Loop
# Includes all Sprint-0 deps plus trading libraries

# Core (from Sprint-0)
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.2.1
python-dotenv==1.0.0
prometheus-client==0.19.0
loguru==0.7.2
redis==5.0.1
sqlalchemy[asyncio]==2.0.23
asyncpg==0.29.0
clickhouse-driver==0.2.6
httpx==0.25.2
pytest==7.4.3
pytest-asyncio==0.21.1
flake8==6.1.0
black==23.11.0

# Market Data & Trading
ccxt==4.1.77
websockets==12.0
aiohttp==3.9.1
pandas==2.1.4
numpy==1.26.4
ta==0.11.0  # Technical Analysis library

# Additional utilities
python-json-logger==2.0.7
tenacity==8.2.3  # Retry logic
aiocache==0.12.2  # Async caching 