"""Pytest configuration shared fixtures & path setup.

Ensures project root is on sys.path so that `import src.*` works
regardless of where pytest collects tests from.
"""

from __future__ import annotations

import sys
from pathlib import Path

# Add project root to sys.path if not already present.
ROOT = Path(__file__).resolve().parent.parent
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT)) 