"""Base agent class.

Provides asynchronous lifecycle, Redis pub/sub using aioredis.
References official aioredis v2.0 API (redis-py >4.5 includes asyncio):
https://redis.readthedocs.io/en/stable/examples/asyncio_examples.html
"""

from __future__ import annotations

import asyncio
import json
import logging
import os
import uuid
from typing import Final

import redis.asyncio as aioredis

from src.core.config import get_settings
from src.core.metrics import AGENT_HEARTBEATS

settings = get_settings()
logger = logging.getLogger(__name__)

HEARTBEAT_INTERVAL: Final[int] = 5  # seconds


class AbstractAgent:  # pylint: disable=too-few-public-methods
    """Skeleton agent used only for Sprint-0 heartbeat verification."""

    NAME: str = "abstract"

    def __init__(self) -> None:
        self._running = False
        self._redis = aioredis.from_url(settings.REDIS_URL, decode_responses=True)
        self._hb_task: asyncio.Task[None] | None = None

    async def start(self) -> None:
        self._running = True
        self._hb_task = asyncio.create_task(self._heartbeat_loop())
        logger.info("Agent %s started", self.NAME)

    async def stop(self) -> None:
        self._running = False
        if self._hb_task:
            await self._hb_task
        await self._redis.close()
        logger.info("Agent %s stopped", self.NAME)

    async def _heartbeat_loop(self) -> None:
        """Publish heartbeat until stop requested."""
        channel = f"agent:{self.NAME}:hb"
        while self._running:
            payload = {"ts": asyncio.get_event_loop().time(), "id": str(uuid.uuid4())}
            try:
                await self._redis.publish(channel, json.dumps(payload))
                # Increment Prometheus counter locally; FastAPI may run
                # in another process but registry is process-wide.
                AGENT_HEARTBEATS.inc()
            except Exception as exc:  # pylint: disable=broad-except
                logger.error("Heartbeat publish failed: %s", exc, exc_info=settings.DEBUG)
            await asyncio.sleep(HEARTBEAT_INTERVAL) 