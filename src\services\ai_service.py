"""
AI Service for Noryon V2 Trading System
Handles communication with Ollama AI models
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional
import aiohttp
from src.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

class AIService:
    """Service for interacting with AI models via Ollama"""
    
    def __init__(self):
        self.base_url = settings.ollama_url
        self.timeout = settings.OLLAMA_TIMEOUT
        
        # Model assignments for different agents
        self.models = {
            "market_watcher": "granite3.3:8b",
            "strategy_researcher": "cogito:32b", 
            "technical_analyst": "gemma3:27b",
            "news_analyst": "magistral:24b",
            "risk_officer": "mistral-small:24b",
            "trade_executor": "falcon3:10b",
            "compliance_auditor": "deepseek-r1:latest",
            "chief_analyst": "command-r:35b",
            "portfolio_manager": "qwen3:32b"
        }
    
    async def generate_response(
        self, 
        agent_type: str, 
        prompt: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Generate AI response for specific agent type"""
        
        model = self.models.get(agent_type, "granite3.3:8b")
        
        # Add context to prompt if provided
        if context:
            context_str = json.dumps(context, indent=2)
            full_prompt = f"Context:\n{context_str}\n\nTask: {prompt}"
        else:
            full_prompt = prompt
            
        try:
            async with aiohttp.ClientSession() as session:
                payload = {
                    "model": model,
                    "prompt": full_prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.7,
                        "top_p": 0.9,
                        "max_tokens": 500
                    }
                }
                
                async with session.post(
                    f"{self.base_url}/api/generate",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=self.timeout)
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        ai_response = result.get('response', '').strip()
                        
                        logger.info(f"AI response from {agent_type} ({model}): {ai_response[:100]}...")
                        return ai_response
                    else:
                        logger.error(f"AI request failed: HTTP {response.status}")
                        return f"AI service unavailable (HTTP {response.status})"
                        
        except asyncio.TimeoutError:
            logger.error(f"AI request timeout for {agent_type}")
            return "AI service timeout - using fallback analysis"
        except Exception as e:
            logger.error(f"AI request error for {agent_type}: {e}")
            return f"AI service error: {str(e)}"
    
    async def analyze_market_data(self, symbol: str, data: Dict[str, Any]) -> str:
        """Market analysis using AI"""
        prompt = f"""
        As a crypto market analyst, analyze the following data for {symbol}:
        
        Price: ${data.get('price', 'N/A')}
        24h Change: {data.get('change_24h', 'N/A')}%
        Volume: {data.get('volume', 'N/A')}
        
        Provide a brief market insight and trend analysis in 2-3 sentences.
        """
        
        return await self.generate_response("market_watcher", prompt, data)
    
    async def generate_trading_strategy(self, market_conditions: Dict[str, Any]) -> str:
        """Generate trading strategy using AI"""
        prompt = f"""
        As a trading strategy researcher, analyze current market conditions and suggest 
        an optimal trading strategy. Consider risk management and market volatility.
        
        Provide specific actionable recommendations.
        """
        
        return await self.generate_response("strategy_researcher", prompt, market_conditions)
    
    async def assess_risk(self, portfolio: Dict[str, Any], position: Dict[str, Any]) -> str:
        """Risk assessment using AI"""
        prompt = f"""
        As a risk officer, evaluate the risk of this trading position:
        
        Position Size: {position.get('size', 'N/A')}
        Entry Price: {position.get('entry_price', 'N/A')}
        Current Price: {position.get('current_price', 'N/A')}
        
        Assess the risk level and provide recommendations.
        """
        
        context = {"portfolio": portfolio, "position": position}
        return await self.generate_response("risk_officer", prompt, context)
    
    async def technical_analysis(self, symbol: str, indicators: Dict[str, Any]) -> str:
        """Technical analysis using AI"""
        prompt = f"""
        As a technical analyst, analyze these indicators for {symbol}:
        
        RSI: {indicators.get('rsi', 'N/A')}
        MACD: {indicators.get('macd', 'N/A')}
        Moving Averages: {indicators.get('ma', 'N/A')}
        
        Provide technical analysis and trading signals.
        """
        
        return await self.generate_response("technical_analyst", prompt, indicators)
    
    async def check_health(self) -> bool:
        """Check if AI service is healthy"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/api/tags",
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    return response.status == 200
        except:
            return False

# Global AI service instance
ai_service = AIService()
