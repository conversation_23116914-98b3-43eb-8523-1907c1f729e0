---
description: 
globs: 
alwaysApply: true
---
Senior AI Systems Developer Rules Set
(For Agentic, Trading, Multi-Agent, Backend, and AI/ML Teams)

Accuracy, Reliability & Anti-Hallucination
Never invent APIs, model names, data structures, or library functions—verify all usage with official documentation or schema.

Mark all speculative code, unknowns, or approximations as "TO VERIFY" and log for human review.

Validate all outputs (code, configs, queries, models) with automated tools, linters, or test environments before integration.

Only use production-ready libraries, APIs, and dependencies—avoid experimental/unmaintained packages in critical systems.

Reference real-world, field-tested design patterns for AI agents, orchestration, and backend integration.

System Architecture, Multi-Agent & Backend
Maintain a system map: keep architectural diagrams, interface definitions, and data flows updated with every change.

Design for modularity—every component/agent/service must be independently testable and replaceable.

Use strong interface contracts (OpenAPI, protobuf, JSON schemas, etc.) for inter-agent and API communication.

Ensure all agents/systems are discoverable, monitorable, and versioned.

Enforce consistency in logging, tracing, and error propagation across all agents and backend services.

Trading & ML-Specific Practices
Never make market or trading logic assumptions—validate all strategies with backtests and simulated data.

Ensure reproducibility of all ML pipelines (training, inference, data versioning, model artifact storage).

Include sanity checks for input data, output signals, and trade execution—catch and log anomalies automatically.

Require explainability for all trading/ML decisions—log feature importances, model decisions, and edge-case handling.

Integrate risk management at every step: fail-safe guards, circuit breakers, and position/risk limits.

Backend Infrastructure & Scalability
Use infrastructure-as-code (e.g., Terraform, CloudFormation) for all infra deployments; no manual server provisioning.

Enforce statelessness in backend agents/services unless explicit state is required; use managed state stores/databases for persistence.

Automate all build, test, deployment, and monitoring pipelines; every agent/system must have automated health checks.

Design for horizontal scaling—agents and backend services must be easily replicated and load-balanced.

Secure all endpoints, credentials, and sensitive data—follow zero-trust and least-privilege principles.

Frontend & User-Facing AI
Clearly separate backend AI logic from frontend/UI code; use APIs or event buses for communication.

Validate all frontend inputs on both client and server sides; never trust user-supplied data in AI inference or trading actions.

Display model confidence, explainability, and possible limitations/warnings in all user-facing AI outputs.

Support real-time updates and observability in frontend dashboards for AI/trading status, errors, and system health.

Continuous Improvement, Knowledge Sharing, and Documentation
Document every non-obvious design choice, trade-off, and failure point in code and architecture docs.

Link all system components to their source of truth (e.g., repo, wiki, schema registry).

Run regular post-mortems for bugs, outages, and missed trades; update system and team processes based on learnings.

Regularly audit, update, and improve this ruleset as technologies, architectures, and best practices evolve.

Encourage active knowledge sharing—publish guides, code samples, and runbooks for all critical workflows.

AI Agent Behavior & Self-Regulation
Before committing or deploying, run a checklist:

 Is every code change verified, tested, and documented?

 Are hallucination-prone areas flagged for review?

 Are all API/model/schema references checked against authoritative sources?

 Is observability (logs, metrics, traces) in place for all new/updated components?

 Are dependencies, security, and scaling implications addressed?