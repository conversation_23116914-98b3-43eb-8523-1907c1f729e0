name: CI

on:
  push:
    branches: [ "main", "master" ]
  pull_request:
    branches: [ "main", "master" ]

jobs:
  build:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_USER: noryon
          POSTGRES_PASSWORD: password
          POSTGRES_DB: noryon_crypto
        ports:
          - 5433:5432
        options: >-
          --health-cmd="pg_isready -U noryon -d noryon_crypto" --health-interval=10s --health-timeout=5s --health-retries=5

      clickhouse:
        image: clickhouse/clickhouse-server:latest
        ports:
          - 8123:8123
        options: >-
          --health-cmd="wget -qO- http://localhost:8123/ping || exit 1" --health-interval=10s --health-timeout=5s --health-retries=5

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Lint with flake8
        run: |
          flake8 src tests --count --select=E9,F63,F7,F82 --show-source --statistics
          flake8 src tests --count --exit-zero --statistics

      - name: Run tests
        env:
          DATABASE_URL: postgresql://noryon:password@localhost:5433/noryon_crypto
          CLICKHOUSE_HOST: localhost
          REDIS_URL: redis://localhost:6379/0
        run: |
          pytest -q 