# =============================================================================
# NORYON CRYPTO TRADING AGENTS - ENVIRONMENT CONFIGURATION
# =============================================================================
# Copy this file to .env and fill in your actual values
# NEVER commit .env to version control!

# =============================================================================
# SYSTEM CONFIGURATION
# =============================================================================
ENVIRONMENT=development  # development, staging, production
DEBUG=true
LOG_LEVEL=INFO
SECRET_KEY=your-super-secret-key-change-this-in-production

# =============================================================================
# OLLAMA CONFIGURATION
# =============================================================================
OLLAMA_HOST=http://localhost:11434
OLLAMA_TIMEOUT=300  # seconds

# Model assignments (change if you want different models for different agents)
MODEL_MARKET_WATCHER=granite3.3:8b
MODEL_STRATEGY_RESEARCHER=cogito:32b
MODEL_TECHNICAL_ANALYST=gemma3:27b
MODEL_NEWS_ANALYST=magistral:24b
MODEL_RISK_OFFICER=mistral-small:24b
MODEL_TRADE_EXECUTOR=falcon3:10b
MODEL_COMPLIANCE_AUDITOR=deepseek-r1:latest
MODEL_CHIEF_ANALYST=command-r:35b
MODEL_PORTFOLIO_MANAGER=qwen3:32b

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL (Main database)
DATABASE_URL=postgresql://noryon:password@localhost:5432/noryon_crypto
POSTGRES_USER=noryon
POSTGRES_PASSWORD=password
POSTGRES_DB=noryon_crypto
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# Redis (Caching & Message Queue)
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# ClickHouse (Time-series data)
CLICKHOUSE_HOST=localhost
CLICKHOUSE_PORT=8123
CLICKHOUSE_USER=default
CLICKHOUSE_PASSWORD=
CLICKHOUSE_DATABASE=crypto_data

# MongoDB (Document storage)
MONGODB_URL=mongodb://localhost:27017/noryon_crypto
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DATABASE=noryon_crypto

# =============================================================================
# CRYPTO EXCHANGE API KEYS
# =============================================================================

# Binance
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET_KEY=your_binance_secret_key
BINANCE_TESTNET=true  # Set to false for live trading

# Coinbase Pro
COINBASE_API_KEY=your_coinbase_api_key
COINBASE_SECRET_KEY=your_coinbase_secret_key
COINBASE_PASSPHRASE=your_coinbase_passphrase
COINBASE_SANDBOX=true  # Set to false for live trading

# Kraken
KRAKEN_API_KEY=your_kraken_api_key
KRAKEN_SECRET_KEY=your_kraken_secret_key

# Bybit
BYBIT_API_KEY=your_bybit_api_key
BYBIT_SECRET_KEY=your_bybit_secret_key
BYBIT_TESTNET=true  # Set to false for live trading

# KuCoin
KUCOIN_API_KEY=your_kucoin_api_key
KUCOIN_SECRET_KEY=your_kucoin_secret_key
KUCOIN_PASSPHRASE=your_kucoin_passphrase
KUCOIN_SANDBOX=true  # Set to false for live trading

# OKX
OKX_API_KEY=your_okx_api_key
OKX_SECRET_KEY=your_okx_secret_key
OKX_PASSPHRASE=your_okx_passphrase
OKX_SANDBOX=true  # Set to false for live trading

# =============================================================================
# DATA PROVIDERS
# =============================================================================

# CoinGecko (Free tier available)
COINGECKO_API_KEY=your_coingecko_api_key

# CoinMarketCap
COINMARKETCAP_API_KEY=your_coinmarketcap_api_key

# Alpha Vantage
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key

# Polygon.io
POLYGON_API_KEY=your_polygon_api_key

# Messari
MESSARI_API_KEY=your_messari_api_key

# =============================================================================
# NEWS & SOCIAL MEDIA APIs
# =============================================================================

# Twitter/X API
TWITTER_BEARER_TOKEN=your_twitter_bearer_token
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret
TWITTER_ACCESS_TOKEN=your_twitter_access_token
TWITTER_ACCESS_TOKEN_SECRET=your_twitter_access_token_secret

# Reddit API
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
REDDIT_USER_AGENT=NoryonCryptoBot/1.0

# News API
NEWS_API_KEY=your_news_api_key

# =============================================================================
# BLOCKCHAIN & DEFI
# =============================================================================

# Ethereum
ETH_RPC_URL=https://mainnet.infura.io/v3/your_infura_project_id
ETH_PRIVATE_KEY=your_ethereum_private_key  # For DeFi interactions
ETH_ADDRESS=your_ethereum_address

# BSC (Binance Smart Chain)
BSC_RPC_URL=https://bsc-dataseed.binance.org/
BSC_PRIVATE_KEY=your_bsc_private_key
BSC_ADDRESS=your_bsc_address

# Polygon
POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/your_infura_project_id
POLYGON_PRIVATE_KEY=your_polygon_private_key
POLYGON_ADDRESS=your_polygon_address

# =============================================================================
# NOTIFICATION SERVICES
# =============================================================================

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# Discord Webhook
DISCORD_WEBHOOK_URL=your_discord_webhook_url

# Slack
SLACK_BOT_TOKEN=your_slack_bot_token
SLACK_CHANNEL=#crypto-alerts

# Email (SendGrid)
SENDGRID_API_KEY=your_sendgrid_api_key
FROM_EMAIL=<EMAIL>
TO_EMAIL=<EMAIL>

# SMS (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number
TO_PHONE_NUMBER=your_phone_number

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================

# Risk Management
MAX_PORTFOLIO_RISK=0.02  # 2% max daily loss
MAX_POSITION_SIZE=0.10   # 10% max position size
MAX_DRAWDOWN=0.15        # 15% max drawdown
STOP_LOSS_PERCENT=0.05   # 5% stop loss

# Trading Parameters
DEFAULT_TRADE_AMOUNT=100  # USD
MIN_TRADE_AMOUNT=10      # USD
MAX_TRADE_AMOUNT=10000   # USD
SLIPPAGE_TOLERANCE=0.005 # 0.5% slippage tolerance

# Supported Trading Pairs (comma-separated)
TRADING_PAIRS=BTC/USDT,ETH/USDT,BNB/USDT,ADA/USDT,SOL/USDT,DOT/USDT,MATIC/USDT,AVAX/USDT,LINK/USDT,UNI/USDT

# =============================================================================
# WEB INTERFACE
# =============================================================================
WEB_HOST=0.0.0.0
WEB_PORT=8000
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# JWT Configuration
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================

# Sentry (Error tracking)
SENTRY_DSN=your_sentry_dsn

# Prometheus
PROMETHEUS_PORT=9090

# Grafana
GRAFANA_URL=http://localhost:3000
GRAFANA_API_KEY=your_grafana_api_key

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================
TESTING=false
MOCK_TRADING=true  # Set to false for live trading
PAPER_TRADING=true # Set to false for live trading

# Logging
LOG_FILE=logs/noryon_crypto.log
LOG_ROTATION=1 day
LOG_RETENTION=30 days

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================
WORKER_PROCESSES=4
MAX_CONNECTIONS=1000
CACHE_TTL=300  # seconds
RATE_LIMIT_PER_MINUTE=60

# =============================================================================
# BACKUP & RECOVERY
# =============================================================================
BACKUP_ENABLED=true
BACKUP_INTERVAL=24  # hours
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=noryon-crypto-backups
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1 