"""Tests for /healthz endpoint (Sprint-0)."""

import pytest
from httpx import AsyncClient

from src.api.main import app


@pytest.mark.asyncio
async def test_health_endpoint_returns_ok() -> None:
    """`/healthz` should return JSON {status:'ok'} and HTTP 200."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        response = await client.get("/healthz")
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}