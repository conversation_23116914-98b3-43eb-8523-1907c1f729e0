"""Tests for market data API endpoints."""

import json
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from httpx import AsyncClient

from src.api.main import app


@pytest.mark.asyncio
async def test_get_latest_tick_from_redis():
    """Test getting latest tick when data exists in Redis."""
    # Mock Redis response
    mock_tick = {
        "symbol": "BTC/USDT",
        "ts": "2024-01-01T12:00:00",
        "bid": 42000.0,
        "ask": 42001.0,
        "last": 42000.5,
        "volume": 1234.56
    }
    
    with patch("src.api.routes.market.redis_client") as mock_redis:
        mock_redis.get = AsyncMock(return_value=json.dumps(mock_tick))
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get("/market/latest?symbol=BTC/USDT")
        
        assert response.status_code == 200
        assert response.json() == mock_tick


@pytest.mark.asyncio
async def test_get_latest_tick_not_found():
    """Test 404 when no tick data exists."""
    with patch("src.api.routes.market.redis_client") as mock_redis:
        mock_redis.get = AsyncMock(return_value=None)
        
        with patch("src.api.routes.market.get_client") as mock_ch:
            mock_ch.return_value.execute.return_value = []
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.get("/market/latest?symbol=XYZ/ABC")
            
            assert response.status_code == 404
            assert "No data found" in response.json()["detail"]


@pytest.mark.asyncio
async def test_get_signal_no_active():
    """Test signal endpoint when no signal exists."""
    with patch("src.api.routes.market.redis_client") as mock_redis:
        mock_redis.get = AsyncMock(return_value=None)
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get("/market/signal?symbol=BTC/USDT")
        
        assert response.status_code == 200
        data = response.json()
        assert data["symbol"] == "BTC/USDT"
        assert data["signal"] is None
        assert "No active signal" in data["message"]


@pytest.mark.asyncio
async def test_get_candles_invalid_interval():
    """Test 400 error for invalid candle interval."""
    with patch("src.api.routes.market.get_client"):  # Mock to avoid real DB
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get("/market/candles?symbol=BTC/USDT&interval=2h")
        
        assert response.status_code == 400
        assert "Invalid interval" in response.json()["detail"] 