#!/usr/bin/env python3
"""
Noryon V2 AI Trading System Launcher
Starts all components of the AI-powered crypto trading system
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/trading_system.log')
    ]
)

logger = logging.getLogger(__name__)

class TradingSystemLauncher:
    """Main launcher for the AI trading system"""
    
    def __init__(self):
        self.components = {}
        self.running = False
        
    async def start_system(self):
        """Start the complete AI trading system"""
        logger.info("🚀 STARTING NORYON V2 AI TRADING SYSTEM")
        logger.info("=" * 60)
        
        try:
            # 1. Start Market Data System
            await self._start_market_data()
            
            # 2. Start AI Service
            await self._start_ai_service()
            
            # 3. Start AI Agents
            await self._start_ai_agents()
            
            # 4. Start API Server (in background)
            await self._start_api_server()
            
            # 5. Start System Monitoring
            await self._start_monitoring()
            
            self.running = True
            logger.info("🎉 NORYON V2 AI TRADING SYSTEM IS FULLY OPERATIONAL!")
            logger.info("=" * 60)
            
            # Print system status
            await self._print_system_status()
            
            # Keep system running
            await self._main_loop()
            
        except Exception as e:
            logger.error(f"❌ System startup failed: {e}")
            await self.stop_system()
            raise
    
    async def _start_market_data(self):
        """Start real market data ingestion"""
        logger.info("📊 Starting Real Market Data System...")

        try:
            from src.services.data_ingestion import data_ingestion

            # Start real data ingestion (includes Binance connector)
            await data_ingestion.start()

            self.components["data_ingestion"] = data_ingestion
            logger.info("✅ Real Market Data System started")

        except Exception as e:
            logger.warning(f"⚠️ Real data failed, falling back to simulator: {e}")

            # Fallback to simulator
            try:
                from src.services.market_simulator import market_broadcaster
                asyncio.create_task(market_broadcaster.start())
                await asyncio.sleep(2)
                self.components["market_data"] = market_broadcaster
                logger.info("✅ Market Data Simulator started as fallback")
            except Exception as fallback_error:
                logger.error(f"❌ Even simulator failed: {fallback_error}")
                raise
    
    async def _start_ai_service(self):
        """Start AI service"""
        logger.info("🤖 Starting AI Service...")
        
        try:
            from src.services.ai_service import ai_service
            
            # Test AI service health
            health = await ai_service.check_health()
            if health:
                logger.info("✅ AI Service connected to Ollama")
            else:
                logger.warning("⚠️ AI Service: Ollama not available, using fallback")
            
            self.components["ai_service"] = ai_service
            logger.info("✅ AI Service initialized")
            
        except Exception as e:
            logger.error(f"❌ AI Service failed: {e}")
            raise
    
    async def _start_ai_agents(self):
        """Start all AI trading agents"""
        logger.info("🧠 Starting AI Trading Agents...")
        
        try:
            from src.agents.agent_manager import agent_manager
            
            # Start all agents
            await agent_manager.start_all_agents()
            
            self.components["agent_manager"] = agent_manager
            logger.info("✅ AI Trading Agents started")
            
        except Exception as e:
            logger.error(f"❌ AI Agents failed: {e}")
            raise
    
    async def _start_api_server(self):
        """Start FastAPI server in background"""
        logger.info("🌐 Starting API Server...")
        
        try:
            # Start API server in background process
            import subprocess
            import os
            
            # Get the virtual environment python path
            venv_python = os.path.join("venv", "Scripts", "python.exe")
            
            # Start uvicorn server
            cmd = [
                venv_python, "-m", "uvicorn", 
                "src.api.main:app", 
                "--host", "0.0.0.0", 
                "--port", "8000",
                "--log-level", "info"
            ]
            
            process = subprocess.Popen(
                cmd, 
                cwd="d:\\noryonv2",
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Wait a moment for server to start
            await asyncio.sleep(3)
            
            self.components["api_server"] = process
            logger.info("✅ API Server started on http://localhost:8000")
            
        except Exception as e:
            logger.error(f"❌ API Server failed: {e}")
            # Don't raise - system can work without API server
    
    async def _start_monitoring(self):
        """Start system monitoring"""
        logger.info("📈 Starting System Monitoring...")
        
        try:
            # Start monitoring task
            asyncio.create_task(self._monitoring_loop())
            
            self.components["monitoring"] = True
            logger.info("✅ System Monitoring started")
            
        except Exception as e:
            logger.error(f"❌ Monitoring failed: {e}")
            # Don't raise - system can work without monitoring
    
    async def _monitoring_loop(self):
        """Monitor system health"""
        while self.running:
            try:
                # Check component health
                await self._health_check()
                
                # Log system metrics
                await self._log_metrics()
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Monitoring error: {e}")
                await asyncio.sleep(10)
    
    async def _health_check(self):
        """Check health of all components"""
        try:
            # Check real data ingestion
            if "data_ingestion" in self.components:
                from src.services.binance_connector import binance_connector
                latest = binance_connector.get_all_latest()
                if latest:
                    logger.debug(f"📊 Real market data: {len(latest)} symbols active")

            # Check market data simulator (fallback)
            elif "market_data" in self.components:
                latest = self.components["market_data"].get_all_latest()
                if latest:
                    logger.debug(f"📊 Simulated market data: {len(latest)} symbols active")

            # Check AI agents
            if "agent_manager" in self.components:
                status = self.components["agent_manager"].get_agent_status()
                active = sum(1 for s in status.values() if s["running"])
                logger.debug(f"🤖 AI Agents: {active}/{len(status)} active")

        except Exception as e:
            logger.error(f"Health check error: {e}")
    
    async def _log_metrics(self):
        """Log system metrics"""
        try:
            if "market_data" in self.components:
                latest = self.components["market_data"].get_all_latest()
                if latest:
                    # Log significant price movements
                    for symbol, tick in latest.items():
                        change = tick.get("change_24h", 0)
                        if abs(change) > 2.0:
                            logger.info(f"📈 {symbol}: ${tick['price']:,.2f} ({change:+.2f}%)")
                            
        except Exception as e:
            logger.error(f"Metrics logging error: {e}")
    
    async def _print_system_status(self):
        """Print current system status"""
        print("\n" + "=" * 60)
        print("🎯 NORYON V2 AI TRADING SYSTEM STATUS")
        print("=" * 60)
        
        for name, component in self.components.items():
            status = "✅ RUNNING" if component else "❌ FAILED"
            print(f"{name.replace('_', ' ').title():.<30} {status}")
        
        print(f"\nSystem Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Components Active: {len(self.components)}")
        
        print("\n🌐 Access Points:")
        print("• API Server: http://localhost:8000")
        print("• API Docs: http://localhost:8000/docs")
        print("• AI Analysis: http://localhost:8000/market/ai-analysis/BTCUSDT")
        
        print("\n🤖 AI Models Available:")
        print("• Market Watcher: granite3.3:8b")
        print("• Strategy Researcher: cogito:32b")
        print("• Technical Analyst: gemma3:27b")
        print("• Risk Officer: mistral-small:24b")
        
        print("\n📊 Trading Pairs:")
        if "market_data" in self.components:
            latest = self.components["market_data"].get_all_latest()
            for symbol, tick in list(latest.items())[:5]:
                print(f"• {symbol}: ${tick['price']:,.2f}")
        
        print("=" * 60)
    
    async def _main_loop(self):
        """Main system loop"""
        logger.info("🔄 Entering main system loop...")
        
        try:
            while self.running:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("🛑 Shutdown signal received")
        except Exception as e:
            logger.error(f"Main loop error: {e}")
        finally:
            await self.stop_system()
    
    async def stop_system(self):
        """Stop the entire trading system"""
        logger.info("⏹️ STOPPING NORYON V2 AI TRADING SYSTEM")
        
        self.running = False
        
        # Stop components in reverse order
        try:
            # Stop API server
            if "api_server" in self.components:
                self.components["api_server"].terminate()
                logger.info("⏹️ API Server stopped")
            
            # Stop AI agents
            if "agent_manager" in self.components:
                await self.components["agent_manager"].stop_all_agents()
                logger.info("⏹️ AI Agents stopped")
            
            # Stop market data
            if "market_data" in self.components:
                self.components["market_data"].stop()
                logger.info("⏹️ Market Data stopped")
            
        except Exception as e:
            logger.error(f"Shutdown error: {e}")
        
        logger.info("🛑 NORYON V2 AI TRADING SYSTEM STOPPED")

async def main():
    """Main entry point"""
    # Ensure logs directory exists
    import os
    os.makedirs("logs", exist_ok=True)
    
    launcher = TradingSystemLauncher()
    
    # Handle shutdown signals
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}")
        asyncio.create_task(launcher.stop_system())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        await launcher.start_system()
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    except Exception as e:
        logger.error(f"System error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    print("🚀 NORYON V2 AI CRYPTO TRADING SYSTEM")
    print("🤖 Powered by 9 AI Models")
    print("📊 Real-time Market Analysis")
    print("⚡ Multi-Agent Coordination")
    print()
    
    asyncio.run(main())
