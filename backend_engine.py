#!/usr/bin/env python3
"""
Noryon V2 - Backend AI Trading Engine
Pure backend system for AI-powered cryptocurrency trading
"""

import asyncio
import logging
import signal
import sys
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional
import random
import time

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/trading_engine.log')
    ]
)

class TradingConfig:
    """Simple configuration for the trading engine"""
    
    def __init__(self):
        self.PAPER_TRADING = True
        self.INITIAL_BALANCE = 10000.0
        self.MAX_POSITION_SIZE = 0.1  # 10% max per position
        self.STOP_LOSS_PERCENTAGE = 0.05  # 5% stop loss
        self.TAKE_PROFIT_PERCENTAGE = 0.15  # 15% take profit
        self.TRADING_PAIRS = [
            "BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT", "SOL/USDT",
            "XRP/USDT", "DOT/USDT", "AVAX/USDT", "MATIC/USDT", "LINK/USDT"
        ]
        self.UPDATE_INTERVAL = 30  # seconds

class MockMarketData:
    """Mock market data provider for backend testing"""
    
    def __init__(self):
        self.prices = {
            "BTC/USDT": 45000.0,
            "ETH/USDT": 3000.0,
            "BNB/USDT": 400.0,
            "ADA/USDT": 0.45,
            "SOL/USDT": 90.0,
            "XRP/USDT": 0.60,
            "DOT/USDT": 7.50,
            "AVAX/USDT": 35.0,
            "MATIC/USDT": 0.85,
            "LINK/USDT": 15.0
        }
        
    def get_price(self, symbol: str) -> float:
        """Get current price for a symbol"""
        base_price = self.prices.get(symbol, 100.0)
        # Add random volatility ±2%
        volatility = random.uniform(-0.02, 0.02)
        return base_price * (1 + volatility)
    
    def update_prices(self):
        """Update all prices with market movement"""
        for symbol in self.prices:
            change = random.uniform(-0.01, 0.01)  # ±1% change
            self.prices[symbol] *= (1 + change)

class AIAgent:
    """Base AI trading agent"""
    
    def __init__(self, name: str, role: str, model: str):
        self.name = name
        self.role = role
        self.model = model
        self.logger = logging.getLogger(f"Agent.{name}")
        self.active = True
        self.last_decision = None
        
    async def analyze(self, market_data: Dict) -> Dict:
        """Analyze market data and make trading decision"""
        # Simulate AI analysis with random decisions
        await asyncio.sleep(0.1)  # Simulate processing time
        
        decision = {
            "agent": self.name,
            "timestamp": datetime.now(),
            "action": random.choice(["BUY", "SELL", "HOLD"]),
            "confidence": random.uniform(0.6, 0.95),
            "reasoning": f"{self.role} analysis using {self.model}"
        }
        
        self.last_decision = decision
        return decision

class Portfolio:
    """Portfolio management system"""
    
    def __init__(self, initial_balance: float):
        self.cash = initial_balance
        self.positions = {}
        self.total_value = initial_balance
        self.trades = []
        self.pnl = 0.0
        
    def execute_trade(self, symbol: str, action: str, amount: float, price: float):
        """Execute a trade"""
        trade = {
            "timestamp": datetime.now(),
            "symbol": symbol,
            "action": action,
            "amount": amount,
            "price": price,
            "value": amount * price
        }
        
        if action == "BUY":
            cost = amount * price
            if cost <= self.cash:
                self.cash -= cost
                if symbol in self.positions:
                    self.positions[symbol] += amount
                else:
                    self.positions[symbol] = amount
                trade["status"] = "FILLED"
                self.trades.append(trade)
                return True
        
        elif action == "SELL":
            if symbol in self.positions and self.positions[symbol] >= amount:
                self.positions[symbol] -= amount
                self.cash += amount * price
                if self.positions[symbol] == 0:
                    del self.positions[symbol]
                trade["status"] = "FILLED"
                self.trades.append(trade)
                return True
        
        trade["status"] = "REJECTED"
        return False
    
    def update_value(self, market_data: MockMarketData):
        """Update portfolio value based on current prices"""
        position_value = 0
        for symbol, amount in self.positions.items():
            current_price = market_data.get_price(symbol)
            position_value += amount * current_price
        
        self.total_value = self.cash + position_value
        self.pnl = self.total_value - 10000.0  # Initial balance

class TradingEngine:
    """Main AI trading engine"""
    
    def __init__(self):
        self.config = TradingConfig()
        self.market_data = MockMarketData()
        self.portfolio = Portfolio(self.config.INITIAL_BALANCE)
        self.logger = logging.getLogger("TradingEngine")
        self.running = False
        
        # Initialize AI agents
        self.agents = {
            "market_watcher": AIAgent("MarketWatcher", "Real-time monitoring", "llama3.2:3b"),
            "technical_analyst": AIAgent("TechnicalAnalyst", "Chart analysis", "gemma2:9b"),
            "risk_manager": AIAgent("RiskManager", "Risk assessment", "qwen2.5:7b"),
            "trader": AIAgent("Trader", "Trade execution", "mistral:7b"),
            "portfolio_manager": AIAgent("PortfolioManager", "Asset allocation", "llama3.2:3b")
        }
        
        self.logger.info("🚀 Noryon V2 Backend Trading Engine Initialized")
        self.logger.info(f"📊 Portfolio: ${self.portfolio.total_value:,.2f}")
        self.logger.info(f"🤖 AI Agents: {len(self.agents)} active")
    
    async def start(self):
        """Start the trading engine"""
        self.running = True
        self.logger.info("🔥 Starting AI Trading Engine...")
        
        # Start all agents
        for name, agent in self.agents.items():
            self.logger.info(f"✅ {name} agent activated ({agent.model})")
        
        # Main trading loop
        cycle_count = 0
        while self.running:
            try:
                cycle_count += 1
                await self._trading_cycle(cycle_count)
                await asyncio.sleep(self.config.UPDATE_INTERVAL)
                
            except KeyboardInterrupt:
                self.logger.info("🛑 Shutdown signal received")
                break
            except Exception as e:
                self.logger.error(f"❌ Error in trading cycle: {e}")
                await asyncio.sleep(5)
    
    async def _trading_cycle(self, cycle: int):
        """Execute one trading cycle"""
        self.logger.info(f"\n{'='*60}")
        self.logger.info(f"🔄 TRADING CYCLE #{cycle} - {datetime.now().strftime('%H:%M:%S')}")
        self.logger.info(f"{'='*60}")
        
        # Update market data
        self.market_data.update_prices()
        
        # Get market snapshot
        market_snapshot = {}
        for symbol in self.config.TRADING_PAIRS[:5]:  # Top 5 pairs
            price = self.market_data.get_price(symbol)
            market_snapshot[symbol] = price
            self.logger.info(f"📈 {symbol}: ${price:,.2f}")
        
        # AI agents analyze market
        decisions = {}
        for name, agent in self.agents.items():
            decision = await agent.analyze(market_snapshot)
            decisions[name] = decision
            
            if decision["action"] != "HOLD":
                self.logger.info(f"🤖 {name}: {decision['action']} "
                               f"(confidence: {decision['confidence']:.1%})")
        
        # Portfolio manager makes final decision
        await self._execute_trading_decisions(decisions, market_snapshot)
        
        # Update portfolio value
        self.portfolio.update_value(self.market_data)
        
        # Log portfolio status
        self.logger.info(f"\n💰 PORTFOLIO STATUS:")
        self.logger.info(f"   Total Value: ${self.portfolio.total_value:,.2f}")
        self.logger.info(f"   Cash: ${self.portfolio.cash:,.2f}")
        self.logger.info(f"   P&L: ${self.portfolio.pnl:,.2f}")
        self.logger.info(f"   Positions: {len(self.portfolio.positions)}")
        
        for symbol, amount in self.portfolio.positions.items():
            current_price = self.market_data.get_price(symbol)
            value = amount * current_price
            self.logger.info(f"     {symbol}: {amount:.4f} (${value:,.2f})")
    
    async def _execute_trading_decisions(self, decisions: Dict, market_data: Dict):
        """Execute trading decisions based on AI agent recommendations"""
        
        # Simple consensus-based trading logic
        buy_votes = sum(1 for d in decisions.values() if d["action"] == "BUY")
        sell_votes = sum(1 for d in decisions.values() if d["action"] == "SELL")
        
        if buy_votes >= 3:  # Majority buy signal
            # Find best buy opportunity
            symbol = random.choice(list(market_data.keys()))
            price = market_data[symbol]
            
            # Calculate position size (max 10% of portfolio)
            max_investment = self.portfolio.total_value * self.config.MAX_POSITION_SIZE
            amount = min(max_investment / price, self.portfolio.cash / price * 0.9)
            
            if amount > 0 and amount * price >= 10:  # Minimum $10 trade
                success = self.portfolio.execute_trade(symbol, "BUY", amount, price)
                if success:
                    self.logger.info(f"✅ TRADE EXECUTED: BUY {amount:.4f} {symbol} @ ${price:,.2f}")
                else:
                    self.logger.warning(f"❌ TRADE REJECTED: Insufficient funds")
        
        elif sell_votes >= 3 and self.portfolio.positions:  # Majority sell signal
            # Sell a random position
            symbol = random.choice(list(self.portfolio.positions.keys()))
            amount = self.portfolio.positions[symbol] * 0.5  # Sell 50%
            price = market_data.get(symbol, self.market_data.get_price(symbol))
            
            success = self.portfolio.execute_trade(symbol, "SELL", amount, price)
            if success:
                self.logger.info(f"✅ TRADE EXECUTED: SELL {amount:.4f} {symbol} @ ${price:,.2f}")
    
    def stop(self):
        """Stop the trading engine"""
        self.running = False
        self.logger.info("🛑 Trading engine stopped")
        
        # Final portfolio report
        self.logger.info(f"\n{'='*60}")
        self.logger.info(f"📊 FINAL PORTFOLIO REPORT")
        self.logger.info(f"{'='*60}")
        self.logger.info(f"Final Value: ${self.portfolio.total_value:,.2f}")
        self.logger.info(f"Total P&L: ${self.portfolio.pnl:,.2f}")
        self.logger.info(f"Total Trades: {len(self.portfolio.trades)}")
        
        if self.portfolio.trades:
            self.logger.info(f"\nRecent Trades:")
            for trade in self.portfolio.trades[-5:]:
                self.logger.info(f"  {trade['timestamp'].strftime('%H:%M:%S')} - "
                               f"{trade['action']} {trade['amount']:.4f} {trade['symbol']} "
                               f"@ ${trade['price']:.2f}")

async def main():
    """Main entry point"""
    
    # Create logs directory
    Path("logs").mkdir(exist_ok=True)
    
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                     NORYON V2 BACKEND TRADING ENGINE                        ║
║                      Advanced AI Crypto Trading System                      ║
║                                                                              ║
║  🤖 5 AI Agents Active                                                       ║
║  📊 Real-time Market Analysis                                                ║
║  💰 Automated Portfolio Management                                           ║
║  🛡️ Paper Trading Mode                                                       ║
║  ⚡ 30-second Trading Cycles                                                 ║
║                                                                              ║
║  Backend-only system - No frontend dependencies!                            ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    # Initialize and start trading engine
    engine = TradingEngine()
    
    # Setup signal handlers
    def signal_handler(signum, frame):
        print("\n🛑 Shutdown signal received...")
        engine.stop()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        await engine.start()
    except KeyboardInterrupt:
        engine.stop()
    except Exception as e:
        logging.error(f"❌ Fatal error: {e}")
        engine.stop()

if __name__ == "__main__":
    asyncio.run(main()) 