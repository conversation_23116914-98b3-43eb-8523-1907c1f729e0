#!/usr/bin/env python3
"""
AI Agent Integration Test for Noryon V2
Tests all AI models and agent functionality
"""

import asyncio
import json
import requests
from typing import Dict, Any
from src.core.config import get_settings
from src.agents.strategy_researcher import <PERSON>Researcher

def test_ollama_connection():
    """Test basic Ollama connectivity"""
    print("🔌 Testing Ollama connection...")
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json()
            print(f"   ✅ Ollama connected! Found {len(models.get('models', []))} models")
            return True
        else:
            print(f"   ❌ Ollama connection failed: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Ollama connection failed: {e}")
        return False

def test_model_inference(model_name: str, prompt: str):
    """Test individual model inference"""
    print(f"🧠 Testing {model_name}...")
    try:
        payload = {
            "model": model_name,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.7,
                "top_p": 0.9,
                "max_tokens": 150
            }
        }
        
        response = requests.post(
            "http://localhost:11434/api/generate",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            ai_response = result.get('response', '').strip()
            print(f"   ✅ {model_name}: {ai_response[:100]}...")
            return True
        else:
            print(f"   ❌ {model_name} failed: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ {model_name} failed: {e}")
        return False

async def test_strategy_researcher():
    """Test the Strategy Researcher agent"""
    print("📊 Testing Strategy Researcher Agent...")
    try:
        agent = StrategyResearcher()
        
        # Test market data analysis
        sample_data = {
            "symbol": "BTC/USDT",
            "price": 45000,
            "volume": 1000000,
            "change_24h": 2.5
        }
        
        # This would normally call the AI model
        print(f"   ✅ Strategy Researcher initialized successfully")
        print(f"   📈 Sample analysis for {sample_data['symbol']}: Price ${sample_data['price']:,}")
        return True
    except Exception as e:
        print(f"   ❌ Strategy Researcher failed: {e}")
        return False

def test_all_ai_models():
    """Test all downloaded AI models"""
    print("🤖 Testing All AI Trading Models...")
    
    models_config = {
        "granite3.3:8b": {
            "role": "Market Watcher",
            "prompt": "Analyze current crypto market conditions in 2 sentences."
        },
        "cogito:32b": {
            "role": "Strategy Researcher", 
            "prompt": "Suggest a trading strategy for volatile crypto markets."
        },
        "gemma3:27b": {
            "role": "Technical Analyst",
            "prompt": "Explain RSI indicator for Bitcoin trading."
        },
        "magistral:24b": {
            "role": "News Analyst",
            "prompt": "How do news events impact crypto prices?"
        },
        "mistral-small:24b": {
            "role": "Risk Officer",
            "prompt": "What are key risk factors in crypto trading?"
        },
        "falcon3:10b": {
            "role": "Trade Executor",
            "prompt": "Explain optimal order execution strategies."
        },
        "deepseek-r1:latest": {
            "role": "Compliance Auditor",
            "prompt": "What compliance checks are needed for crypto trading?"
        },
        "command-r:35b": {
            "role": "Chief Analyst",
            "prompt": "Provide a comprehensive market outlook for cryptocurrencies."
        },
        "qwen3:32b": {
            "role": "Portfolio Manager",
            "prompt": "How to optimize a crypto portfolio allocation?"
        }
    }
    
    results = {}
    for model, config in models_config.items():
        success = test_model_inference(model, config["prompt"])
        results[model] = {
            "role": config["role"],
            "status": "✅ PASS" if success else "❌ FAIL"
        }
    
    return results

async def run_ai_integration_test():
    """Run comprehensive AI integration test"""
    print("🚀 NORYON V2 AI AGENT INTEGRATION TEST")
    print("=" * 60)
    
    # Test 1: Ollama Connection
    ollama_ok = test_ollama_connection()
    if not ollama_ok:
        print("\n❌ Cannot proceed without Ollama connection!")
        return False
    
    print()
    
    # Test 2: Individual Model Testing
    model_results = test_all_ai_models()
    
    print()
    
    # Test 3: Strategy Researcher Agent
    strategy_ok = await test_strategy_researcher()
    
    print()
    print("=" * 60)
    print("📋 AI INTEGRATION TEST SUMMARY")
    print("=" * 60)
    
    # Summary
    passed = sum(1 for r in model_results.values() if "PASS" in r["status"])
    total = len(model_results)
    
    for model, result in model_results.items():
        print(f"{result['role']:.<25} {result['status']}")
    
    print(f"\nStrategy Researcher Agent.... {'✅ PASS' if strategy_ok else '❌ FAIL'}")
    print(f"Ollama Connection............ {'✅ PASS' if ollama_ok else '❌ FAIL'}")
    
    print(f"\nOverall: {passed}/{total} AI models working")
    
    if passed == total and strategy_ok and ollama_ok:
        print("\n🎉 ALL AI AGENTS READY FOR TRADING!")
        print("\n🚀 Next Steps:")
        print("1. Start Redis for agent communication")
        print("2. Launch the trading agents")
        print("3. Begin live market analysis")
        return True
    else:
        print(f"\n⚠️  {total - passed} models need attention")
        return False

if __name__ == "__main__":
    success = asyncio.run(run_ai_integration_test())
    exit(0 if success else 1)
