"""Integration tests for database connectivity (Sprint-0).

These tests require running Postgres and ClickHouse instances reachable
at the URLs specified in src.core.config.Settings. If they are not
available the tests are skipped.
"""

import asyncio

import pytest
from sqlalchemy import text

from src.db.postgres import async_engine, get_async_session
from src.db.clickhouse import ping as clickhouse_ping
from src.db.redis import ping as redis_ping  # noqa: E402  # imported early to satisfy flake8

pytestmark = pytest.mark.asyncio


async def can_connect_postgres() -> bool:  # noqa: D401
    """Return True if Postgres responds to SELECT 1 else False."""
    try:
        async with async_engine.begin() as conn:
            await conn.execute(text("SELECT 1"))
        return True
    except Exception:  # pylint: disable=broad-except
        return False


@pytest.mark.asyncio
async def test_postgres_select_1() -> None:
    """Ensure Postgres responds to a simple SELECT 1 or skip if unavailable."""
    if not await can_connect_postgres():
        pytest.skip("Postgres not available")

    async with get_async_session() as session:
        result = await session.execute(text("SELECT 1"))
        assert result.scalar_one() == 1


@pytest.mark.asyncio
async def test_clickhouse_ping() -> None:
    """Ensure ClickHouse ping returns True or skip."""
    if not clickhouse_ping():
        pytest.skip("ClickHouse not available")

    assert clickhouse_ping() is True


# ---------------------------------------------------------------------------
# Redis Connectivity
# ---------------------------------------------------------------------------


@pytest.mark.asyncio
async def test_redis_ping() -> None:
    """Ensure Redis responds to PING or skip the test if unavailable."""
    if not await redis_ping():
        pytest.skip("Redis not available")

    assert await redis_ping() is True 