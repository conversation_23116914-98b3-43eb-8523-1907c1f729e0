"""Strategy Researcher Agent - Generates trading signals from market data.

Sprint-1 implementation: Simple Moving Average (SMA) crossover strategy.
Subscribes to tick data via Redis, maintains rolling price windows,
and publishes BUY/SELL signals when crossovers occur.
"""

from __future__ import annotations

import asyncio
import json
from collections import deque
from datetime import datetime
from typing import Any, Deque, Dict, Optional

import pandas as pd
import redis.asyncio as aioredis
from loguru import logger

from src.agents.base import AbstractAgent
from src.core.config import get_settings
from src.core.metrics import STRATEGY_SIGNALS
from src.services.ai_service import ai_service

settings = get_settings()


class StrategyResearcher(AbstractAgent):
    """Analyzes market data and generates trading signals."""
    
    NAME = "strategy_researcher"
    
    def __init__(self) -> None:
        super().__init__()
        self.pubsub: Optional[aioredis.client.PubSub] = None
        self.price_windows: Dict[str, Deque[float]] = {}
        self.last_signals: Dict[str, str] = {}  # Track last signal per symbol
        self._subscriber_task: Optional[asyncio.Task] = None
        
        # Strategy parameters
        self.fast_period = 50
        self.slow_period = 200
        self.window_size = self.slow_period + 50  # Extra buffer

    async def start(self) -> None:
        """Start the agent and subscribe to tick channels."""
        await super().start()
        
        # Initialize Redis pubsub (if Redis is available)
        try:
            self.pubsub = self._redis.pubsub() if self._redis else None
        except Exception as e:
            logger.warning(f"Redis pubsub unavailable: {e}")
            self.pubsub = None
        
        # Subscribe to all tick channels
        if isinstance(settings.TRADING_PAIRS, list):
            pairs = settings.TRADING_PAIRS
        else:
            pairs = [p.strip() for p in settings.TRADING_PAIRS.split(",") if p.strip()]

        # Initialize price windows for all symbols
        for symbol in pairs:
            self.price_windows[symbol] = deque(maxlen=self.window_size)

        # Subscribe only if pubsub is available
        if self.pubsub:
            for symbol in pairs:
                channel = f"ticks:{symbol}"
                await self.pubsub.subscribe(channel)
                logger.info(f"Subscribed to {channel}")
        else:
            logger.info("No Redis pubsub available, will run in standalone mode")
        
        # Start message processing or standalone mode
        if self.pubsub:
            self._subscriber_task = asyncio.create_task(self._process_messages())
        else:
            logger.info("Running in standalone mode without Redis")
            self._subscriber_task = asyncio.create_task(self._standalone_analysis_loop())

    async def stop(self) -> None:
        """Stop the agent and cleanup."""
        if self._subscriber_task:
            self._subscriber_task.cancel()
            await asyncio.gather(self._subscriber_task, return_exceptions=True)
        
        if self.pubsub:
            await self.pubsub.unsubscribe()
            await self.pubsub.aclose()
        
        await super().stop()

    async def _process_messages(self) -> None:
        """Process incoming tick messages."""
        async for message in self.pubsub.listen():
            if message["type"] != "message":
                continue
                
            try:
                # Parse tick data
                channel = message["channel"]
                symbol = channel.split(":")[-1]
                tick = json.loads(message["data"])
                
                # Update price window
                self.price_windows[symbol].append(tick["last"])
                
                # Check for signals if we have enough data
                if len(self.price_windows[symbol]) >= self.slow_period:
                    await self._check_signal(symbol)
                    
            except Exception as exc:
                logger.error(f"Error processing message: {exc}")

    async def _check_signal(self, symbol: str) -> None:
        """Check for SMA crossover signals."""
        prices = list(self.price_windows[symbol])
        
        # Calculate SMAs
        df = pd.DataFrame({"price": prices})
        df["sma_fast"] = df["price"].rolling(window=self.fast_period).mean()
        df["sma_slow"] = df["price"].rolling(window=self.slow_period).mean()
        
        # Get latest values
        current = df.iloc[-1]
        previous = df.iloc[-2]
        
        # Skip if NaN
        if pd.isna(current["sma_fast"]) or pd.isna(current["sma_slow"]):
            return
        
        # Detect crossovers
        signal = None
        
        # Bullish crossover: fast crosses above slow
        if (previous["sma_fast"] <= previous["sma_slow"] and 
            current["sma_fast"] > current["sma_slow"]):
            signal = "BUY"
            
        # Bearish crossover: fast crosses below slow
        elif (previous["sma_fast"] >= previous["sma_slow"] and 
              current["sma_fast"] < current["sma_slow"]):
            signal = "SELL"
        
        # Publish signal if changed
        if signal and self.last_signals.get(symbol) != signal:
            await self._publish_signal(symbol, signal, current["price"])
            self.last_signals[symbol] = signal

    async def _publish_signal(self, symbol: str, side: str, price: float) -> None:
        """Publish trading signal to Redis with AI analysis."""

        # Get AI-powered analysis
        ai_insight = "Technical analysis only"
        try:
            market_context = {
                "symbol": symbol,
                "signal": side,
                "price": price,
                "strategy": "sma_crossover"
            }
            ai_insight = await ai_service.generate_trading_strategy(market_context)
        except Exception as e:
            logger.warning(f"AI analysis failed for {symbol}: {e}")

        signal_data = {
            "symbol": symbol,
            "strategy": "sma_crossover",
            "side": side,
            "price": price,
            "confidence": 0.7,  # Fixed confidence for v1
            "ai_insight": ai_insight[:200],  # Truncate for storage
            "ts": datetime.utcnow().isoformat(),
        }
        
        # Publish to Redis pubsub
        channel = f"signals:{symbol}"
        signal_json = json.dumps(signal_data)
        await self._redis.publish(channel, signal_json)
        
        # Also save latest signal for API queries
        await self._redis.set(f"signal:latest:{symbol}", signal_json, ex=3600)  # 1 hour TTL
        
        # Update metrics
        STRATEGY_SIGNALS.labels(
            strategy="sma_crossover",
            symbol=symbol,
            side=side
        ).inc()
        
        logger.info(f"Signal published: {symbol} {side} @ {price:.2f}")

    async def _standalone_analysis_loop(self):
        """Run analysis loop without Redis - using market simulator data"""
        while self._running:
            try:
                # Get market data from the simulator
                from src.services.market_simulator import market_broadcaster

                latest_data = market_broadcaster.get_all_latest()

                for symbol, tick in latest_data.items():
                    # Simulate price window updates
                    if symbol not in self.price_windows:
                        self.price_windows[symbol] = deque(maxlen=self.window_size)

                    self.price_windows[symbol].append(tick.get("price", 0))

                    # Check for signals if we have enough data
                    if len(self.price_windows[symbol]) >= self.slow_period:
                        await self._check_signal(symbol)

                await asyncio.sleep(5)  # Check every 5 seconds

            except Exception as e:
                logger.error(f"Standalone analysis error: {e}")
                await asyncio.sleep(10)


async def main() -> None:
    """Standalone entry point."""
    from src.core.logger import configure
    configure(file_sink=True)
    
    agent = StrategyResearcher()
    
    try:
        await agent.start()
        # Run indefinitely
        await asyncio.Event().wait()
    except KeyboardInterrupt:
        logger.info("Shutting down...")
    finally:
        await agent.stop()


if __name__ == "__main__":
    asyncio.run(main()) 