"""
Noryon V2 - Database Manager
Comprehensive database management for PostgreSQL, Redis, ClickHouse, and MongoDB
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
import json
from decimal import Decimal

import asyncpg
import aioredis
import aioclickhouse
import motor.motor_asyncio
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text

from src.core.config import Config
from src.core.logger import get_logger, get_performance_logger

class DatabaseManager:
    """Comprehensive database manager for all database systems"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger(__name__)
        self.performance_logger = get_performance_logger("database")
        
        # Database connections
        self.postgres_engine = None
        self.postgres_session_factory = None
        self.redis_client = None
        self.clickhouse_client = None
        self.mongodb_client = None
        self.mongodb_db = None
        
        # Connection pools
        self.postgres_pool = None
        
        # Health status
        self.connection_status = {
            "postgres": False,
            "redis": False,
            "clickhouse": False,
            "mongodb": False
        }
    
    async def initialize(self):
        """Initialize all database connections"""
        try:
            self.logger.info("🗄️ Initializing database connections...")
            
            # Initialize PostgreSQL
            await self._initialize_postgres()
            
            # Initialize Redis
            await self._initialize_redis()
            
            # Initialize ClickHouse
            await self._initialize_clickhouse()
            
            # Initialize MongoDB
            await self._initialize_mongodb()
            
            # Create database schemas
            await self._create_schemas()
            
            self.logger.info("✅ All database connections initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize databases: {e}")
            raise
    
    async def _initialize_postgres(self):
        """Initialize PostgreSQL connection"""
        try:
            # Create async engine
            self.postgres_engine = create_async_engine(
                self.config.postgres_url.replace("postgresql://", "postgresql+asyncpg://"),
                pool_size=20,
                max_overflow=30,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=self.config.DEBUG
            )
            
            # Create session factory
            self.postgres_session_factory = sessionmaker(
                self.postgres_engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            # Test connection
            async with self.postgres_engine.begin() as conn:
                await conn.execute(text("SELECT 1"))
            
            self.connection_status["postgres"] = True
            self.logger.info("✅ PostgreSQL connection established")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize PostgreSQL: {e}")
            raise
    
    async def _initialize_redis(self):
        """Initialize Redis connection"""
        try:
            self.redis_client = aioredis.from_url(
                self.config.redis_url,
                encoding="utf-8",
                decode_responses=True,
                max_connections=20,
                retry_on_timeout=True,
                socket_keepalive=True,
                socket_keepalive_options={}
            )
            
            # Test connection
            await self.redis_client.ping()
            
            self.connection_status["redis"] = True
            self.logger.info("✅ Redis connection established")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Redis: {e}")
            raise
    
    async def _initialize_clickhouse(self):
        """Initialize ClickHouse connection"""
        try:
            self.clickhouse_client = aioclickhouse.create_client(
                host=self.config.CLICKHOUSE_HOST,
                port=self.config.CLICKHOUSE_PORT,
                user=self.config.CLICKHOUSE_USER,
                password=self.config.CLICKHOUSE_PASSWORD,
                database=self.config.CLICKHOUSE_DB
            )
            
            # Test connection
            await self.clickhouse_client.execute("SELECT 1")
            
            self.connection_status["clickhouse"] = True
            self.logger.info("✅ ClickHouse connection established")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize ClickHouse: {e}")
            raise
    
    async def _initialize_mongodb(self):
        """Initialize MongoDB connection"""
        try:
            self.mongodb_client = motor.motor_asyncio.AsyncIOMotorClient(
                self.config.mongodb_url,
                maxPoolSize=20,
                minPoolSize=5,
                maxIdleTimeMS=30000,
                serverSelectionTimeoutMS=5000
            )
            
            self.mongodb_db = self.mongodb_client[self.config.MONGODB_DB]
            
            # Test connection
            await self.mongodb_client.admin.command('ping')
            
            self.connection_status["mongodb"] = True
            self.logger.info("✅ MongoDB connection established")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize MongoDB: {e}")
            raise
    
    async def _create_schemas(self):
        """Create database schemas and tables"""
        try:
            # PostgreSQL schemas
            await self._create_postgres_schemas()
            
            # ClickHouse schemas
            await self._create_clickhouse_schemas()
            
            # MongoDB collections
            await self._create_mongodb_collections()
            
            self.logger.info("✅ Database schemas created successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to create schemas: {e}")
            raise
    
    async def _create_postgres_schemas(self):
        """Create PostgreSQL schemas and tables"""
        postgres_schema = """
        -- Users and authentication
        CREATE TABLE IF NOT EXISTS users (
            id SERIAL PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Trading pairs
        CREATE TABLE IF NOT EXISTS trading_pairs (
            id SERIAL PRIMARY KEY,
            symbol VARCHAR(20) UNIQUE NOT NULL,
            base_asset VARCHAR(10) NOT NULL,
            quote_asset VARCHAR(10) NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            min_trade_amount DECIMAL(20, 8),
            max_trade_amount DECIMAL(20, 8),
            price_precision INTEGER DEFAULT 8,
            quantity_precision INTEGER DEFAULT 8,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Exchanges
        CREATE TABLE IF NOT EXISTS exchanges (
            id SERIAL PRIMARY KEY,
            name VARCHAR(50) UNIQUE NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            api_url VARCHAR(255),
            websocket_url VARCHAR(255),
            trading_fees DECIMAL(5, 4) DEFAULT 0.001,
            withdrawal_fees JSONB,
            supported_pairs JSONB,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Portfolio
        CREATE TABLE IF NOT EXISTS portfolios (
            id SERIAL PRIMARY KEY,
            user_id INTEGER REFERENCES users(id),
            name VARCHAR(100) NOT NULL,
            initial_balance DECIMAL(20, 8) NOT NULL,
            current_balance DECIMAL(20, 8) NOT NULL,
            total_pnl DECIMAL(20, 8) DEFAULT 0,
            is_paper_trading BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Positions
        CREATE TABLE IF NOT EXISTS positions (
            id SERIAL PRIMARY KEY,
            portfolio_id INTEGER REFERENCES portfolios(id),
            symbol VARCHAR(20) NOT NULL,
            side VARCHAR(10) NOT NULL, -- 'long' or 'short'
            quantity DECIMAL(20, 8) NOT NULL,
            entry_price DECIMAL(20, 8) NOT NULL,
            current_price DECIMAL(20, 8),
            unrealized_pnl DECIMAL(20, 8) DEFAULT 0,
            realized_pnl DECIMAL(20, 8) DEFAULT 0,
            stop_loss DECIMAL(20, 8),
            take_profit DECIMAL(20, 8),
            is_open BOOLEAN DEFAULT TRUE,
            opened_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            closed_at TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Orders
        CREATE TABLE IF NOT EXISTS orders (
            id SERIAL PRIMARY KEY,
            portfolio_id INTEGER REFERENCES portfolios(id),
            exchange_id INTEGER REFERENCES exchanges(id),
            symbol VARCHAR(20) NOT NULL,
            order_type VARCHAR(20) NOT NULL, -- 'market', 'limit', 'stop', etc.
            side VARCHAR(10) NOT NULL, -- 'buy' or 'sell'
            quantity DECIMAL(20, 8) NOT NULL,
            price DECIMAL(20, 8),
            filled_quantity DECIMAL(20, 8) DEFAULT 0,
            average_price DECIMAL(20, 8),
            status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'filled', 'cancelled', 'rejected'
            exchange_order_id VARCHAR(100),
            fees DECIMAL(20, 8) DEFAULT 0,
            strategy VARCHAR(50),
            agent_source VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            filled_at TIMESTAMP
        );
        
        -- Trades (executed orders)
        CREATE TABLE IF NOT EXISTS trades (
            id SERIAL PRIMARY KEY,
            order_id INTEGER REFERENCES orders(id),
            portfolio_id INTEGER REFERENCES portfolios(id),
            exchange_id INTEGER REFERENCES exchanges(id),
            symbol VARCHAR(20) NOT NULL,
            side VARCHAR(10) NOT NULL,
            quantity DECIMAL(20, 8) NOT NULL,
            price DECIMAL(20, 8) NOT NULL,
            value DECIMAL(20, 8) NOT NULL,
            fees DECIMAL(20, 8) DEFAULT 0,
            pnl DECIMAL(20, 8),
            strategy VARCHAR(50),
            agent_source VARCHAR(50),
            exchange_trade_id VARCHAR(100),
            executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- AI Agents
        CREATE TABLE IF NOT EXISTS ai_agents (
            id SERIAL PRIMARY KEY,
            name VARCHAR(50) UNIQUE NOT NULL,
            model_name VARCHAR(100) NOT NULL,
            role VARCHAR(255) NOT NULL,
            status VARCHAR(20) DEFAULT 'inactive',
            last_heartbeat TIMESTAMP,
            configuration JSONB,
            performance_metrics JSONB,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Trading signals
        CREATE TABLE IF NOT EXISTS trading_signals (
            id SERIAL PRIMARY KEY,
            agent_id INTEGER REFERENCES ai_agents(id),
            symbol VARCHAR(20) NOT NULL,
            action VARCHAR(10) NOT NULL, -- 'buy', 'sell', 'hold'
            strength DECIMAL(3, 2) NOT NULL, -- 0.00 to 1.00
            confidence DECIMAL(3, 2) NOT NULL,
            price_target DECIMAL(20, 8),
            stop_loss DECIMAL(20, 8),
            take_profit DECIMAL(20, 8),
            strategy VARCHAR(50),
            reasoning TEXT,
            risk_score DECIMAL(3, 2),
            is_processed BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- System metrics
        CREATE TABLE IF NOT EXISTS system_metrics (
            id SERIAL PRIMARY KEY,
            metric_name VARCHAR(100) NOT NULL,
            metric_value DECIMAL(20, 8),
            metric_data JSONB,
            recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Risk metrics
        CREATE TABLE IF NOT EXISTS risk_metrics (
            id SERIAL PRIMARY KEY,
            portfolio_id INTEGER REFERENCES portfolios(id),
            var_95 DECIMAL(20, 8),
            max_drawdown DECIMAL(20, 8),
            sharpe_ratio DECIMAL(10, 4),
            beta DECIMAL(10, 4),
            portfolio_risk DECIMAL(5, 4),
            risk_data JSONB,
            calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Create indexes for better performance
        CREATE INDEX IF NOT EXISTS idx_orders_portfolio_symbol ON orders(portfolio_id, symbol);
        CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
        CREATE INDEX IF NOT EXISTS idx_trades_portfolio_symbol ON trades(portfolio_id, symbol);
        CREATE INDEX IF NOT EXISTS idx_trades_executed_at ON trades(executed_at);
        CREATE INDEX IF NOT EXISTS idx_positions_portfolio_symbol ON positions(portfolio_id, symbol);
        CREATE INDEX IF NOT EXISTS idx_positions_is_open ON positions(is_open);
        CREATE INDEX IF NOT EXISTS idx_signals_agent_symbol ON trading_signals(agent_id, symbol);
        CREATE INDEX IF NOT EXISTS idx_signals_created_at ON trading_signals(created_at);
        CREATE INDEX IF NOT EXISTS idx_system_metrics_name_time ON system_metrics(metric_name, recorded_at);
        """
        
        async with self.postgres_engine.begin() as conn:
            await conn.execute(text(postgres_schema))
    
    async def _create_clickhouse_schemas(self):
        """Create ClickHouse schemas for analytics"""
        clickhouse_schema = """
        -- Market data (OHLCV)
        CREATE TABLE IF NOT EXISTS market_data (
            timestamp DateTime64(3),
            symbol String,
            open Float64,
            high Float64,
            low Float64,
            close Float64,
            volume Float64,
            exchange String,
            timeframe String
        ) ENGINE = MergeTree()
        ORDER BY (symbol, timestamp);
        
        -- Price ticks
        CREATE TABLE IF NOT EXISTS price_ticks (
            timestamp DateTime64(3),
            symbol String,
            price Float64,
            volume Float64,
            bid Float64,
            ask Float64,
            exchange String
        ) ENGINE = MergeTree()
        ORDER BY (symbol, timestamp);
        
        -- Order book snapshots
        CREATE TABLE IF NOT EXISTS order_book_snapshots (
            timestamp DateTime64(3),
            symbol String,
            exchange String,
            bids Array(Tuple(Float64, Float64)),
            asks Array(Tuple(Float64, Float64))
        ) ENGINE = MergeTree()
        ORDER BY (symbol, timestamp);
        
        -- Trade executions
        CREATE TABLE IF NOT EXISTS trade_executions (
            timestamp DateTime64(3),
            symbol String,
            side String,
            quantity Float64,
            price Float64,
            value Float64,
            fees Float64,
            exchange String,
            strategy String,
            agent_source String,
            portfolio_id UInt32
        ) ENGINE = MergeTree()
        ORDER BY (timestamp, symbol);
        
        -- Performance metrics
        CREATE TABLE IF NOT EXISTS performance_metrics (
            timestamp DateTime64(3),
            portfolio_id UInt32,
            total_value Float64,
            pnl Float64,
            daily_pnl Float64,
            drawdown Float64,
            sharpe_ratio Float64,
            win_rate Float64,
            total_trades UInt32
        ) ENGINE = MergeTree()
        ORDER BY (portfolio_id, timestamp);
        
        -- Agent performance
        CREATE TABLE IF NOT EXISTS agent_performance (
            timestamp DateTime64(3),
            agent_name String,
            signals_generated UInt32,
            successful_signals UInt32,
            accuracy Float64,
            avg_confidence Float64,
            processing_time_ms Float64
        ) ENGINE = MergeTree()
        ORDER BY (agent_name, timestamp);
        
        -- System events
        CREATE TABLE IF NOT EXISTS system_events (
            timestamp DateTime64(3),
            event_type String,
            component String,
            severity String,
            message String,
            metadata String
        ) ENGINE = MergeTree()
        ORDER BY (timestamp, event_type);
        """
        
        for statement in clickhouse_schema.split(';'):
            if statement.strip():
                await self.clickhouse_client.execute(statement.strip())
    
    async def _create_mongodb_collections(self):
        """Create MongoDB collections and indexes"""
        try:
            # Create collections
            collections = [
                'news_articles',
                'market_sentiment',
                'agent_logs',
                'system_logs',
                'research_reports',
                'backtesting_results',
                'strategy_configurations',
                'alert_notifications'
            ]
            
            for collection_name in collections:
                if collection_name not in await self.mongodb_db.list_collection_names():
                    await self.mongodb_db.create_collection(collection_name)
            
            # Create indexes
            await self._create_mongodb_indexes()
            
        except Exception as e:
            self.logger.error(f"Error creating MongoDB collections: {e}")
            raise
    
    async def _create_mongodb_indexes(self):
        """Create MongoDB indexes"""
        try:
            # News articles indexes
            await self.mongodb_db.news_articles.create_index([
                ("published_at", -1),
                ("symbol", 1)
            ])
            
            # Market sentiment indexes
            await self.mongodb_db.market_sentiment.create_index([
                ("timestamp", -1),
                ("symbol", 1)
            ])
            
            # Agent logs indexes
            await self.mongodb_db.agent_logs.create_index([
                ("timestamp", -1),
                ("agent_name", 1),
                ("log_level", 1)
            ])
            
            # System logs indexes
            await self.mongodb_db.system_logs.create_index([
                ("timestamp", -1),
                ("component", 1),
                ("severity", 1)
            ])
            
        except Exception as e:
            self.logger.error(f"Error creating MongoDB indexes: {e}")
            raise
    
    # PostgreSQL Methods
    
    async def get_postgres_session(self) -> AsyncSession:
        """Get a PostgreSQL session"""
        return self.postgres_session_factory()
    
    async def execute_postgres_query(self, query: str, params: Dict = None) -> List[Dict]:
        """Execute a PostgreSQL query"""
        async with self.get_postgres_session() as session:
            result = await session.execute(text(query), params or {})
            return [dict(row) for row in result.fetchall()]
    
    async def insert_trade(self, trade_data: Dict[str, Any]) -> int:
        """Insert a trade record"""
        query = """
        INSERT INTO trades (portfolio_id, exchange_id, symbol, side, quantity, price, value, fees, strategy, agent_source)
        VALUES (:portfolio_id, :exchange_id, :symbol, :side, :quantity, :price, :value, :fees, :strategy, :agent_source)
        RETURNING id
        """
        
        async with self.get_postgres_session() as session:
            result = await session.execute(text(query), trade_data)
            trade_id = result.scalar()
            await session.commit()
            return trade_id
    
    async def insert_order(self, order_data: Dict[str, Any]) -> int:
        """Insert an order record"""
        query = """
        INSERT INTO orders (portfolio_id, exchange_id, symbol, order_type, side, quantity, price, strategy, agent_source)
        VALUES (:portfolio_id, :exchange_id, :symbol, :order_type, :side, :quantity, :price, :strategy, :agent_source)
        RETURNING id
        """
        
        async with self.get_postgres_session() as session:
            result = await session.execute(text(query), order_data)
            order_id = result.scalar()
            await session.commit()
            return order_id
    
    async def update_position(self, portfolio_id: int, symbol: str, quantity: float, price: float):
        """Update or create a position"""
        query = """
        INSERT INTO positions (portfolio_id, symbol, side, quantity, entry_price, current_price)
        VALUES (:portfolio_id, :symbol, :side, :quantity, :entry_price, :current_price)
        ON CONFLICT (portfolio_id, symbol) 
        DO UPDATE SET 
            quantity = positions.quantity + EXCLUDED.quantity,
            current_price = EXCLUDED.current_price,
            updated_at = CURRENT_TIMESTAMP
        """
        
        side = 'long' if quantity > 0 else 'short'
        
        async with self.get_postgres_session() as session:
            await session.execute(text(query), {
                'portfolio_id': portfolio_id,
                'symbol': symbol,
                'side': side,
                'quantity': abs(quantity),
                'entry_price': price,
                'current_price': price
            })
            await session.commit()
    
    async def insert_trading_signal(self, signal_data: Dict[str, Any]) -> int:
        """Insert a trading signal"""
        query = """
        INSERT INTO trading_signals (agent_id, symbol, action, strength, confidence, price_target, stop_loss, take_profit, strategy, reasoning, risk_score)
        VALUES (:agent_id, :symbol, :action, :strength, :confidence, :price_target, :stop_loss, :take_profit, :strategy, :reasoning, :risk_score)
        RETURNING id
        """
        
        async with self.get_postgres_session() as session:
            result = await session.execute(text(query), signal_data)
            signal_id = result.scalar()
            await session.commit()
            return signal_id
    
    # Redis Methods
    
    async def cache_market_data(self, symbol: str, data: Dict[str, Any], ttl: int = 300):
        """Cache market data in Redis"""
        key = f"market_data:{symbol}"
        await self.redis_client.setex(key, ttl, json.dumps(data, default=str))
    
    async def get_cached_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get cached market data from Redis"""
        key = f"market_data:{symbol}"
        data = await self.redis_client.get(key)
        return json.loads(data) if data else None
    
    async def cache_portfolio_state(self, portfolio_id: int, state: Dict[str, Any], ttl: int = 60):
        """Cache portfolio state in Redis"""
        key = f"portfolio:{portfolio_id}"
        await self.redis_client.setex(key, ttl, json.dumps(state, default=str))
    
    async def get_cached_portfolio_state(self, portfolio_id: int) -> Optional[Dict[str, Any]]:
        """Get cached portfolio state from Redis"""
        key = f"portfolio:{portfolio_id}"
        data = await self.redis_client.get(key)
        return json.loads(data) if data else None
    
    async def set_agent_status(self, agent_name: str, status: str, ttl: int = 300):
        """Set agent status in Redis"""
        key = f"agent_status:{agent_name}"
        await self.redis_client.setex(key, ttl, status)
    
    async def get_agent_status(self, agent_name: str) -> Optional[str]:
        """Get agent status from Redis"""
        key = f"agent_status:{agent_name}"
        return await self.redis_client.get(key)
    
    async def publish_message(self, channel: str, message: Dict[str, Any]):
        """Publish a message to Redis pub/sub"""
        await self.redis_client.publish(channel, json.dumps(message, default=str))
    
    async def subscribe_to_channel(self, channel: str):
        """Subscribe to a Redis pub/sub channel"""
        pubsub = self.redis_client.pubsub()
        await pubsub.subscribe(channel)
        return pubsub
    
    # ClickHouse Methods
    
    async def insert_market_data(self, data: List[Dict[str, Any]]):
        """Insert market data into ClickHouse"""
        if not data:
            return
        
        query = """
        INSERT INTO market_data (timestamp, symbol, open, high, low, close, volume, exchange, timeframe)
        VALUES
        """
        
        values = []
        for item in data:
            values.append(f"('{item['timestamp']}', '{item['symbol']}', {item['open']}, {item['high']}, {item['low']}, {item['close']}, {item['volume']}, '{item['exchange']}', '{item['timeframe']}')")
        
        full_query = query + ", ".join(values)
        await self.clickhouse_client.execute(full_query)
    
    async def insert_price_tick(self, tick_data: Dict[str, Any]):
        """Insert a price tick into ClickHouse"""
        query = """
        INSERT INTO price_ticks (timestamp, symbol, price, volume, bid, ask, exchange)
        VALUES (%(timestamp)s, %(symbol)s, %(price)s, %(volume)s, %(bid)s, %(ask)s, %(exchange)s)
        """
        
        await self.clickhouse_client.execute(query, tick_data)
    
    async def get_market_data_range(self, symbol: str, start_time: datetime, end_time: datetime, timeframe: str = '1m') -> List[Dict]:
        """Get market data for a time range"""
        query = """
        SELECT timestamp, open, high, low, close, volume
        FROM market_data
        WHERE symbol = %(symbol)s 
        AND timeframe = %(timeframe)s
        AND timestamp BETWEEN %(start_time)s AND %(end_time)s
        ORDER BY timestamp
        """
        
        result = await self.clickhouse_client.execute(query, {
            'symbol': symbol,
            'timeframe': timeframe,
            'start_time': start_time,
            'end_time': end_time
        })
        
        return [dict(row) for row in result]
    
    async def get_performance_metrics(self, portfolio_id: int, days: int = 30) -> List[Dict]:
        """Get performance metrics for a portfolio"""
        query = """
        SELECT timestamp, total_value, pnl, daily_pnl, drawdown, sharpe_ratio, win_rate
        FROM performance_metrics
        WHERE portfolio_id = %(portfolio_id)s
        AND timestamp >= %(start_time)s
        ORDER BY timestamp
        """
        
        start_time = datetime.utcnow() - timedelta(days=days)
        
        result = await self.clickhouse_client.execute(query, {
            'portfolio_id': portfolio_id,
            'start_time': start_time
        })
        
        return [dict(row) for row in result]
    
    # MongoDB Methods
    
    async def insert_news_article(self, article_data: Dict[str, Any]):
        """Insert a news article into MongoDB"""
        collection = self.mongodb_db.news_articles
        result = await collection.insert_one(article_data)
        return str(result.inserted_id)
    
    async def get_recent_news(self, symbol: str = None, hours: int = 24) -> List[Dict]:
        """Get recent news articles"""
        collection = self.mongodb_db.news_articles
        
        query = {
            'published_at': {
                '$gte': datetime.utcnow() - timedelta(hours=hours)
            }
        }
        
        if symbol:
            query['symbols'] = symbol
        
        cursor = collection.find(query).sort('published_at', -1).limit(100)
        return await cursor.to_list(length=100)
    
    async def insert_market_sentiment(self, sentiment_data: Dict[str, Any]):
        """Insert market sentiment data"""
        collection = self.mongodb_db.market_sentiment
        result = await collection.insert_one(sentiment_data)
        return str(result.inserted_id)
    
    async def get_market_sentiment(self, symbol: str, hours: int = 24) -> List[Dict]:
        """Get market sentiment for a symbol"""
        collection = self.mongodb_db.market_sentiment
        
        query = {
            'symbol': symbol,
            'timestamp': {
                '$gte': datetime.utcnow() - timedelta(hours=hours)
            }
        }
        
        cursor = collection.find(query).sort('timestamp', -1)
        return await cursor.to_list(length=None)
    
    async def insert_agent_log(self, log_data: Dict[str, Any]):
        """Insert agent log entry"""
        collection = self.mongodb_db.agent_logs
        result = await collection.insert_one(log_data)
        return str(result.inserted_id)
    
    async def insert_research_report(self, report_data: Dict[str, Any]):
        """Insert research report"""
        collection = self.mongodb_db.research_reports
        result = await collection.insert_one(report_data)
        return str(result.inserted_id)
    
    # Health and Monitoring
    
    async def check_connections(self) -> Dict[str, bool]:
        """Check all database connections"""
        status = {}
        
        # Check PostgreSQL
        try:
            async with self.postgres_engine.begin() as conn:
                await conn.execute(text("SELECT 1"))
            status['postgres'] = True
        except Exception:
            status['postgres'] = False
        
        # Check Redis
        try:
            await self.redis_client.ping()
            status['redis'] = True
        except Exception:
            status['redis'] = False
        
        # Check ClickHouse
        try:
            await self.clickhouse_client.execute("SELECT 1")
            status['clickhouse'] = True
        except Exception:
            status['clickhouse'] = False
        
        # Check MongoDB
        try:
            await self.mongodb_client.admin.command('ping')
            status['mongodb'] = True
        except Exception:
            status['mongodb'] = False
        
        self.connection_status = status
        return status
    
    async def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics"""
        stats = {}
        
        try:
            # PostgreSQL stats
            pg_stats = await self.execute_postgres_query("""
                SELECT 
                    schemaname,
                    tablename,
                    n_tup_ins as inserts,
                    n_tup_upd as updates,
                    n_tup_del as deletes
                FROM pg_stat_user_tables
            """)
            stats['postgres'] = pg_stats
        except Exception as e:
            stats['postgres'] = {'error': str(e)}
        
        try:
            # Redis stats
            redis_info = await self.redis_client.info()
            stats['redis'] = {
                'used_memory': redis_info.get('used_memory_human'),
                'connected_clients': redis_info.get('connected_clients'),
                'total_commands_processed': redis_info.get('total_commands_processed')
            }
        except Exception as e:
            stats['redis'] = {'error': str(e)}
        
        try:
            # MongoDB stats
            mongo_stats = await self.mongodb_db.command('dbStats')
            stats['mongodb'] = {
                'collections': mongo_stats.get('collections'),
                'dataSize': mongo_stats.get('dataSize'),
                'indexSize': mongo_stats.get('indexSize')
            }
        except Exception as e:
            stats['mongodb'] = {'error': str(e)}
        
        return stats
    
    async def close(self):
        """Close all database connections"""
        try:
            if self.postgres_engine:
                await self.postgres_engine.dispose()
            
            if self.redis_client:
                await self.redis_client.close()
            
            if self.clickhouse_client:
                await self.clickhouse_client.close()
            
            if self.mongodb_client:
                self.mongodb_client.close()
            
            self.logger.info("✅ All database connections closed")
            
        except Exception as e:
            self.logger.error(f"Error closing database connections: {e}")
            raise 