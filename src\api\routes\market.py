"""Market data API routes.

Provides endpoints to query latest ticks and strategy signals.
"""

from __future__ import annotations

import json
from datetime import datetime
from typing import Any, Dict, Optional

import redis.asyncio as aioredis
from fastapi import APIRouter, HTTPException, Query
from loguru import logger

from src.core.config import get_settings
from src.db.clickhouse import get_client
from src.services.ai_service import ai_service

settings = get_settings()
router = APIRouter(prefix="/market", tags=["market"])

# Redis client for latest data
redis_client = aioredis.from_url(settings.redis_url, decode_responses=True)


@router.get("/latest")
async def get_latest_tick(
    symbol: str = Query(..., description="Trading pair symbol (e.g., BTC/USDT)")
) -> Dict[str, Any]:
    """Get the latest tick data for a symbol."""
    try:
        # Try to get from Redis first (most recent)
        tick_data = await redis_client.get(f"tick:latest:{symbol}")
        
        if tick_data:
            return json.loads(tick_data)
        
        # Fallback to ClickHouse
        client = get_client()
        result = client.execute(
            """
            SELECT symbol, ts, bid, ask, last, volume
            FROM ticks
            WHERE symbol = %(symbol)s
            ORDER BY ts DESC
            LIMIT 1
            """,
            {"symbol": symbol}
        )
        
        if not result:
            raise HTTPException(status_code=404, detail=f"No data found for {symbol}")
        
        row = result[0]
        return {
            "symbol": row[0],
            "ts": row[1].isoformat(),
            "bid": row[2],
            "ask": row[3],
            "last": row[4],
            "volume": row[5],
        }
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as exc:
        logger.error(f"Error fetching latest tick: {exc}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/candles")
async def get_candles(
    symbol: str = Query(..., description="Trading pair symbol"),
    interval: str = Query("1m", description="Candle interval (1m, 5m, 15m, 1h)"),
    limit: int = Query(100, ge=1, le=1000, description="Number of candles to return")
) -> Dict[str, Any]:
    """Get historical candlestick data."""
    try:
        # Map interval to ClickHouse function
        interval_map = {
            "1m": "toStartOfMinute",
            "5m": "toStartOfFiveMinutes", 
            "15m": "toStartOfFifteenMinutes",
            "1h": "toStartOfHour",
        }
        
        if interval not in interval_map:
            raise HTTPException(status_code=400, detail="Invalid interval")
        
        client = get_client()
        
        # Use materialized view for 1m, calculate others on the fly
        if interval == "1m":
            query = """
                SELECT ts, open, high, low, close, volume
                FROM candles_1m
                WHERE symbol = %(symbol)s
                ORDER BY ts DESC
                LIMIT %(limit)s
            """
        else:
            func = interval_map[interval]
            query = f"""
                SELECT 
                    {func}(ts) as ts,
                    argMin(last, ts) as open,
                    max(last) as high,
                    min(last) as low,
                    argMax(last, ts) as close,
                    sum(volume) as volume
                FROM ticks
                WHERE symbol = %(symbol)s
                GROUP BY {func}(ts)
                ORDER BY ts DESC
                LIMIT %(limit)s
            """
        
        result = client.execute(query, {"symbol": symbol, "limit": limit})
        
        candles = []
        for row in result:
            candles.append({
                "ts": row[0].isoformat(),
                "open": row[1],
                "high": row[2],
                "low": row[3],
                "close": row[4],
                "volume": row[5],
            })
        
        return {
            "symbol": symbol,
            "interval": interval,
            "candles": list(reversed(candles))  # Return in chronological order
        }
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as exc:
        logger.error(f"Error fetching candles: {exc}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/signal")
async def get_latest_signal(
    symbol: str = Query(..., description="Trading pair symbol")
) -> Dict[str, Any]:
    """Get the latest strategy signal for a symbol."""
    try:
        # Get from Redis
        signal_data = await redis_client.get(f"signal:latest:{symbol}")
        
        if not signal_data:
            return {
                "symbol": symbol,
                "signal": None,
                "message": "No active signal"
            }
        
        signal = json.loads(signal_data)
        return {
            "symbol": symbol,
            "signal": signal
        }
        
    except Exception as exc:
        logger.error(f"Error fetching signal: {exc}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/stats")
async def get_market_stats() -> Dict[str, Any]:
    """Get market data ingestion statistics."""
    try:
        client = get_client()
        
        # Get tick counts by symbol
        result = client.execute("""
            SELECT 
                symbol,
                count() as tick_count,
                min(ts) as first_tick,
                max(ts) as last_tick,
                avg(last) as avg_price
            FROM ticks
            WHERE ts > now() - INTERVAL 1 HOUR
            GROUP BY symbol
        """)
        
        stats = {}
        for row in result:
            stats[row[0]] = {
                "tick_count": row[1],
                "first_tick": row[2].isoformat(),
                "last_tick": row[3].isoformat(),
                "avg_price": row[4],
            }
        
        return {
            "symbols": stats,
            "total_symbols": len(stats),
        }
        
    except Exception as exc:
        logger.error(f"Error fetching stats: {exc}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/ai-analysis/{symbol}")
async def get_ai_analysis(symbol: str) -> Dict[str, Any]:
    """Get AI-powered market analysis for a symbol."""

    try:
        # Get latest market data from Redis
        latest_tick = await redis_client.get(f"tick:latest:{symbol}")

        if latest_tick:
            tick_data = json.loads(latest_tick)
            market_data = {
                "symbol": symbol,
                "price": tick_data.get("last", 0),
                "volume": tick_data.get("volume", 0),
                "timestamp": tick_data.get("ts", "")
            }
        else:
            # Fallback mock data
            market_data = {
                "symbol": symbol,
                "price": 45000.0,
                "change_24h": 2.5,
                "volume": 1000000
            }

        # Get AI analysis
        analysis = await ai_service.analyze_market_data(symbol, market_data)

        return {
            "symbol": symbol,
            "market_data": market_data,
            "ai_analysis": analysis,
            "timestamp": market_data.get("timestamp", "2024-01-01T00:00:00Z"),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"AI analysis error for {symbol}: {e}")
        return {
            "symbol": symbol,
            "ai_analysis": f"AI analysis unavailable: {str(e)}",
            "timestamp": "2024-01-01T00:00:00Z",
            "status": "error"
        }


@router.get("/live-data")
async def get_live_market_data() -> Dict[str, Any]:
    """Get live market data from simulator"""
    try:
        from src.services.market_simulator import market_broadcaster

        latest_data = market_broadcaster.get_all_latest()

        if not latest_data:
            return {
                "message": "No live data available - market simulator may not be running",
                "symbols": [],
                "timestamp": datetime.utcnow().isoformat()
            }

        return {
            "symbols": latest_data,
            "count": len(latest_data),
            "timestamp": datetime.utcnow().isoformat(),
            "status": "live"
        }

    except Exception as e:
        logger.error(f"Live data error: {e}")
        raise HTTPException(status_code=500, detail=f"Live data error: {str(e)}")


@router.get("/ai-agents/status")
async def get_ai_agents_status() -> Dict[str, Any]:
    """Get status of all AI trading agents"""
    try:
        from src.agents.agent_manager import agent_manager

        status = agent_manager.get_agent_status()

        return {
            "agents": status,
            "total_agents": len(status),
            "active_agents": sum(1 for s in status.values() if s.get("running", False)),
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Agent status error: {e}")
        return {
            "agents": {},
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }