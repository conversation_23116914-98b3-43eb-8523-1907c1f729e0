"""Market data API routes.

Provides endpoints to query latest ticks and strategy signals.
"""

from __future__ import annotations

import json
from typing import Any, Dict, Optional

import redis.asyncio as aioredis
from fastapi import APIRouter, HTTPException, Query
from loguru import logger

from src.core.config import get_settings
from src.db.clickhouse import get_client

settings = get_settings()
router = APIRouter(prefix="/market", tags=["market"])

# Redis client for latest data
redis_client = aioredis.from_url(settings.redis_url, decode_responses=True)


@router.get("/latest")
async def get_latest_tick(
    symbol: str = Query(..., description="Trading pair symbol (e.g., BTC/USDT)")
) -> Dict[str, Any]:
    """Get the latest tick data for a symbol."""
    try:
        # Try to get from Redis first (most recent)
        tick_data = await redis_client.get(f"tick:latest:{symbol}")
        
        if tick_data:
            return json.loads(tick_data)
        
        # Fallback to ClickHouse
        client = get_client()
        result = client.execute(
            """
            SELECT symbol, ts, bid, ask, last, volume
            FROM ticks
            WHERE symbol = %(symbol)s
            ORDER BY ts DESC
            LIMIT 1
            """,
            {"symbol": symbol}
        )
        
        if not result:
            raise HTTPException(status_code=404, detail=f"No data found for {symbol}")
        
        row = result[0]
        return {
            "symbol": row[0],
            "ts": row[1].isoformat(),
            "bid": row[2],
            "ask": row[3],
            "last": row[4],
            "volume": row[5],
        }
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as exc:
        logger.error(f"Error fetching latest tick: {exc}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/candles")
async def get_candles(
    symbol: str = Query(..., description="Trading pair symbol"),
    interval: str = Query("1m", description="Candle interval (1m, 5m, 15m, 1h)"),
    limit: int = Query(100, ge=1, le=1000, description="Number of candles to return")
) -> Dict[str, Any]:
    """Get historical candlestick data."""
    try:
        # Map interval to ClickHouse function
        interval_map = {
            "1m": "toStartOfMinute",
            "5m": "toStartOfFiveMinutes", 
            "15m": "toStartOfFifteenMinutes",
            "1h": "toStartOfHour",
        }
        
        if interval not in interval_map:
            raise HTTPException(status_code=400, detail="Invalid interval")
        
        client = get_client()
        
        # Use materialized view for 1m, calculate others on the fly
        if interval == "1m":
            query = """
                SELECT ts, open, high, low, close, volume
                FROM candles_1m
                WHERE symbol = %(symbol)s
                ORDER BY ts DESC
                LIMIT %(limit)s
            """
        else:
            func = interval_map[interval]
            query = f"""
                SELECT 
                    {func}(ts) as ts,
                    argMin(last, ts) as open,
                    max(last) as high,
                    min(last) as low,
                    argMax(last, ts) as close,
                    sum(volume) as volume
                FROM ticks
                WHERE symbol = %(symbol)s
                GROUP BY {func}(ts)
                ORDER BY ts DESC
                LIMIT %(limit)s
            """
        
        result = client.execute(query, {"symbol": symbol, "limit": limit})
        
        candles = []
        for row in result:
            candles.append({
                "ts": row[0].isoformat(),
                "open": row[1],
                "high": row[2],
                "low": row[3],
                "close": row[4],
                "volume": row[5],
            })
        
        return {
            "symbol": symbol,
            "interval": interval,
            "candles": list(reversed(candles))  # Return in chronological order
        }
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as exc:
        logger.error(f"Error fetching candles: {exc}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/signal")
async def get_latest_signal(
    symbol: str = Query(..., description="Trading pair symbol")
) -> Dict[str, Any]:
    """Get the latest strategy signal for a symbol."""
    try:
        # Get from Redis
        signal_data = await redis_client.get(f"signal:latest:{symbol}")
        
        if not signal_data:
            return {
                "symbol": symbol,
                "signal": None,
                "message": "No active signal"
            }
        
        signal = json.loads(signal_data)
        return {
            "symbol": symbol,
            "signal": signal
        }
        
    except Exception as exc:
        logger.error(f"Error fetching signal: {exc}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/stats")
async def get_market_stats() -> Dict[str, Any]:
    """Get market data ingestion statistics."""
    try:
        client = get_client()
        
        # Get tick counts by symbol
        result = client.execute("""
            SELECT 
                symbol,
                count() as tick_count,
                min(ts) as first_tick,
                max(ts) as last_tick,
                avg(last) as avg_price
            FROM ticks
            WHERE ts > now() - INTERVAL 1 HOUR
            GROUP BY symbol
        """)
        
        stats = {}
        for row in result:
            stats[row[0]] = {
                "tick_count": row[1],
                "first_tick": row[2].isoformat(),
                "last_tick": row[3].isoformat(),
                "avg_price": row[4],
            }
        
        return {
            "symbols": stats,
            "total_symbols": len(stats),
        }
        
    except Exception as exc:
        logger.error(f"Error fetching stats: {exc}")
        raise HTTPException(status_code=500, detail="Internal server error") 