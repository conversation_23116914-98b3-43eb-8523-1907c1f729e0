#!/usr/bin/env python3
"""
Quick AI Agent Test - Focus on fast models first
"""

import requests
import json

def test_fast_models():
    """Test the fastest AI models first"""
    print("🚀 QUICK AI MODEL TEST")
    print("=" * 40)
    
    # Start with the smallest/fastest models
    fast_models = [
        ("granite3.3:8b", "Market Watcher"),
        ("deepseek-r1:latest", "Compliance Auditor"), 
        ("falcon3:10b", "Trade Executor")
    ]
    
    for model, role in fast_models:
        print(f"🧠 Testing {model} ({role})...")
        try:
            payload = {
                "model": model,
                "prompt": f"As a {role} for crypto trading, give me one key insight in 1 sentence.",
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "max_tokens": 50
                }
            }
            
            response = requests.post(
                "http://localhost:11434/api/generate",
                json=payload,
                timeout=15  # Shorter timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '').strip()
                print(f"   ✅ {ai_response}")
            else:
                print(f"   ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        print()

def test_ollama_status():
    """Check Ollama status"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ Ollama running with {len(models)} models")
            return True
    except:
        print("❌ Ollama not accessible")
        return False

if __name__ == "__main__":
    print("🔌 Checking Ollama...")
    if test_ollama_status():
        print()
        test_fast_models()
        print("🎯 Ready to integrate with trading system!")
    else:
        print("Please start Ollama first: ollama serve")
