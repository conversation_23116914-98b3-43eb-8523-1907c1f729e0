"""
Real-time Market Data Simulator for Noryon V2
Simulates live crypto market data for AI agent testing
"""

import asyncio
import json
import random
import time
from datetime import datetime, timezone
from typing import Dict, List, Any
import logging

logger = logging.getLogger(__name__)

class MarketDataSimulator:
    """Simulates real-time crypto market data"""
    
    def __init__(self):
        self.symbols = [
            "BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT", "SOL/USDT",
            "DOT/USDT", "MATIC/USDT", "AVAX/USDT", "LINK/USDT", "UNI/USDT"
        ]
        
        # Starting prices (realistic as of 2024)
        self.prices = {
            "BTC/USDT": 45000.0,
            "ETH/USDT": 2800.0,
            "BNB/USDT": 320.0,
            "ADA/USDT": 0.45,
            "SOL/USDT": 95.0,
            "DOT/USDT": 6.5,
            "MATIC/USDT": 0.85,
            "AVAX/USDT": 28.0,
            "LINK/USDT": 15.0,
            "UNI/USDT": 7.2
        }
        
        self.running = False
        self.subscribers = []
        
    def add_subscriber(self, callback):
        """Add a callback function to receive market data"""
        self.subscribers.append(callback)
        
    def remove_subscriber(self, callback):
        """Remove a subscriber"""
        if callback in self.subscribers:
            self.subscribers.remove(callback)
    
    async def start(self):
        """Start the market data simulation"""
        self.running = True
        logger.info("🚀 Market Data Simulator started")
        
        while self.running:
            try:
                # Generate tick for random symbol
                symbol = random.choice(self.symbols)
                tick = self._generate_tick(symbol)
                
                # Notify all subscribers
                for callback in self.subscribers:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(tick)
                        else:
                            callback(tick)
                    except Exception as e:
                        logger.error(f"Error notifying subscriber: {e}")
                
                # Wait before next tick (1-5 seconds)
                await asyncio.sleep(random.uniform(1.0, 5.0))
                
            except Exception as e:
                logger.error(f"Error in market simulation: {e}")
                await asyncio.sleep(1.0)
    
    def stop(self):
        """Stop the market data simulation"""
        self.running = False
        logger.info("Market Data Simulator stopped")
    
    def _generate_tick(self, symbol: str) -> Dict[str, Any]:
        """Generate a realistic market tick"""
        current_price = self.prices[symbol]
        
        # Generate price movement (-2% to +2%)
        change_percent = random.uniform(-0.02, 0.02)
        new_price = current_price * (1 + change_percent)
        
        # Add some volatility spikes occasionally
        if random.random() < 0.05:  # 5% chance of spike
            spike = random.uniform(-0.05, 0.05)
            new_price = current_price * (1 + spike)
        
        # Update stored price
        self.prices[symbol] = new_price
        
        # Generate volume (realistic ranges)
        base_volume = {
            "BTC/USDT": 50000,
            "ETH/USDT": 30000,
            "BNB/USDT": 15000,
            "ADA/USDT": 100000,
            "SOL/USDT": 25000,
            "DOT/USDT": 20000,
            "MATIC/USDT": 80000,
            "AVAX/USDT": 18000,
            "LINK/USDT": 22000,
            "UNI/USDT": 25000
        }
        
        volume = base_volume.get(symbol, 10000) * random.uniform(0.5, 2.0)
        
        tick = {
            "symbol": symbol,
            "price": round(new_price, 8),
            "last": round(new_price, 8),  # Alias for compatibility
            "volume": round(volume, 2),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "change_24h": round(change_percent * 100, 2),
            "high_24h": round(new_price * 1.05, 8),
            "low_24h": round(new_price * 0.95, 8),
            "bid": round(new_price * 0.999, 8),
            "ask": round(new_price * 1.001, 8),
            "count": random.randint(100, 1000)
        }
        
        return tick

class MarketDataBroadcaster:
    """Broadcasts market data to AI agents and storage"""
    
    def __init__(self):
        self.simulator = MarketDataSimulator()
        self.latest_ticks = {}
        
    async def start(self):
        """Start broadcasting market data"""
        # Subscribe to simulator
        self.simulator.add_subscriber(self._handle_tick)
        
        # Start simulation
        await self.simulator.start()
    
    def stop(self):
        """Stop broadcasting"""
        self.simulator.stop()
    
    async def _handle_tick(self, tick: Dict[str, Any]):
        """Handle incoming market tick"""
        symbol = tick["symbol"]
        self.latest_ticks[symbol] = tick
        
        # Log significant price movements
        if abs(tick["change_24h"]) > 1.0:
            logger.info(f"Price movement {symbol}: ${tick['price']:,.2f} ({tick['change_24h']:+.2f}%)")
        
        # Here we would normally:
        # 1. Store to ClickHouse
        # 2. Publish to Redis
        # 3. Trigger AI analysis
        
        # For now, just store locally
        await self._trigger_ai_analysis(tick)
    
    async def _trigger_ai_analysis(self, tick: Dict[str, Any]):
        """Trigger AI analysis for significant price movements"""
        try:
            # Only analyze significant movements to avoid spam
            if abs(tick["change_24h"]) > 0.5:
                from src.services.ai_service import ai_service
                
                analysis = await ai_service.analyze_market_data(
                    tick["symbol"], 
                    tick
                )
                
                logger.info(f"AI Analysis for {tick['symbol']}: {analysis[:100]}...")
                
        except Exception as e:
            logger.warning(f"AI analysis failed: {e}")
    
    def get_latest_tick(self, symbol: str) -> Dict[str, Any]:
        """Get latest tick for a symbol"""
        return self.latest_ticks.get(symbol, {})
    
    def get_all_latest(self) -> Dict[str, Dict[str, Any]]:
        """Get all latest ticks"""
        return self.latest_ticks.copy()

# Global market data broadcaster
market_broadcaster = MarketDataBroadcaster()

async def start_market_data():
    """Start the market data system"""
    logger.info("Starting Market Data System...")
    await market_broadcaster.start()

def stop_market_data():
    """Stop the market data system"""
    logger.info("Stopping Market Data System...")
    market_broadcaster.stop()

if __name__ == "__main__":
    # Test the market simulator
    async def test_simulator():
        def print_tick(tick):
            print(f"📊 {tick['symbol']}: ${tick['price']:,.2f} ({tick['change_24h']:+.2f}%)")
        
        simulator = MarketDataSimulator()
        simulator.add_subscriber(print_tick)
        
        print("🚀 Starting market simulation test...")
        try:
            await asyncio.wait_for(simulator.start(), timeout=30)
        except asyncio.TimeoutError:
            print("⏹️ Test completed")
            simulator.stop()
    
    asyncio.run(test_simulator())
