#!/usr/bin/env python3
"""
Noryon V2 - Fixed Quick Setup Script
Handles Python 3.13 and Windows compatibility issues
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_step(message):
    print(f"🔧 {message}")

def print_success(message):
    print(f"✅ {message}")

def print_warning(message):
    print(f"⚠️  {message}")

def print_error(message):
    print(f"❌ {message}")

def run_command(command, description):
    """Run a command and handle errors"""
    print_step(f"{description}...")
    try:
        if platform.system() == "Windows":
            result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        else:
            result = subprocess.run(command.split(), check=True, capture_output=True, text=True)
        print_success(f"{description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print_error(f"{description} failed: {e}")
        return False

def main():
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                           NORYON V2 FIXED SETUP                             ║
║                      Advanced AI Crypto Trading System                      ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    print(f"System: {platform.system()}")
    print(f"Python: {platform.python_version()}")
    print(f"Working Directory: {os.getcwd()}")
    
    # Step 1: Create virtual environment
    venv_path = project_root / "venv"
    if not venv_path.exists():
        if not run_command(f"{sys.executable} -m venv venv", "Creating virtual environment"):
            return False
    else:
        print_success("Virtual environment already exists")
    
    # Step 2: Set up pip commands
    if platform.system() == "Windows":
        pip_cmd = "venv\\Scripts\\pip"
        python_cmd = "venv\\Scripts\\python"
    else:
        pip_cmd = "venv/bin/pip"
        python_cmd = "venv/bin/python"
    
    # Step 3: Upgrade pip
    run_command(f"{pip_cmd} install --upgrade pip", "Upgrading pip")
    
    # Step 4: Install Python 3.13 compatible packages
    print_step("Installing Python 3.13 compatible packages...")
    
    # Core packages that work with Python 3.13
    compatible_packages = [
        "fastapi==0.104.1",
        "uvicorn[standard]==0.24.0", 
        "pydantic==2.5.0",
        "pydantic-settings==2.1.0",
        "aiohttp==3.9.1",
        "aioredis==2.0.1",
        "sqlalchemy[asyncio]==2.0.23",
        "ccxt",  # Use latest version
        "python-binance==1.0.19",
        "websockets==12.0",
        "pandas",  # Use latest version
        "requests==2.31.0",
        "python-dotenv==1.0.0",
        "loguru==0.7.2",
        "click==8.1.7",
        "rich",
        "colorama",
        "tqdm",
        "httpx",
        "python-multipart",
        "cryptography",
        "python-jose[cryptography]",
        "passlib[bcrypt]",
        "redis",
        "celery",
        "schedule",
        "apscheduler",
        "marshmallow",
        "jsonschema",
        "python-dateutil",
        "pytz",
        "humanize",
        "pytest",
        "pytest-asyncio",
        "black",
        "flake8",
        "mypy",
        "structlog",
        "prometheus-client",
        "sentry-sdk",
        "textblob",
        "vaderSentiment",
        "yfinance",
        "alpha-vantage",
        "newsapi-python",
        "discord.py",
        "slack-sdk",
        "sendgrid",
        "openpyxl",
        "pillow",
        "boto3",
        "psutil",
        "retrying",
        "tenacity",
        "cachetools",
        "ipython",
        "jupyter",
        "notebook"
    ]
    
    # Install packages one by one
    for package in compatible_packages:
        if not run_command(f"{pip_cmd} install {package}", f"Installing {package.split('==')[0]}"):
            print_warning(f"Failed to install {package}, continuing...")
    
    # Step 5: Try to install problematic packages with fallbacks
    print_step("Installing packages with fallbacks...")
    
    # Try asyncpg with fallback
    if not run_command(f"{pip_cmd} install asyncpg", "Installing asyncpg (latest)"):
        print_warning("asyncpg failed, will use psycopg2-binary instead")
        run_command(f"{pip_cmd} install psycopg2-binary", "Installing psycopg2-binary")
    
    # Try numpy with fallback
    if not run_command(f"{pip_cmd} install numpy", "Installing numpy (latest)"):
        print_warning("numpy failed, some features may not work")
    
    # Try scipy with fallback  
    if not run_command(f"{pip_cmd} install scipy", "Installing scipy (latest)"):
        print_warning("scipy failed, some analysis features may not work")
    
    # Try scikit-learn
    if not run_command(f"{pip_cmd} install scikit-learn", "Installing scikit-learn (latest)"):
        print_warning("scikit-learn failed, ML features may not work")
    
    # Try matplotlib
    if not run_command(f"{pip_cmd} install matplotlib", "Installing matplotlib (latest)"):
        print_warning("matplotlib failed, plotting features may not work")
    
    # Try plotly
    run_command(f"{pip_cmd} install plotly", "Installing plotly")
    
    # Try technical analysis
    run_command(f"{pip_cmd} install ta", "Installing ta (technical analysis)")
    
    # Step 6: Create environment file
    print_step("Creating environment configuration...")
    if not Path(".env").exists():
        try:
            with open("env.example", "r", encoding='utf-8') as src, open(".env", "w", encoding='utf-8') as dst:
                dst.write(src.read())
            print_success("Created .env file from template")
        except FileNotFoundError:
            print_warning("env.example not found, creating basic .env file")
            with open(".env", "w", encoding='utf-8') as f:
                f.write("""# Noryon V2 Configuration
DEBUG=true
LOG_LEVEL=INFO
DATABASE_URL=postgresql://noryon:password@localhost:5432/noryon_trading
REDIS_URL=redis://localhost:6379/0

# Exchange API Keys (Add your keys here)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
COINBASE_API_KEY=your_coinbase_api_key_here
COINBASE_SECRET_KEY=your_coinbase_secret_key_here

# Ollama Configuration
OLLAMA_HOST=http://localhost:11434
""")
    
    # Step 7: Test the installation
    print_step("Testing installation...")
    test_script = """
import sys
try:
    import fastapi
    import uvicorn
    import pydantic
    import aiohttp
    import ccxt
    import pandas
    import requests
    print("Core dependencies imported successfully")
    sys.exit(0)
except ImportError as e:
    print(f"Import error: {e}")
    sys.exit(1)
"""
    
    with open("test_imports.py", "w", encoding='utf-8') as f:
        f.write(test_script)
    
    if run_command(f"{python_cmd} test_imports.py", "Testing core imports"):
        os.remove("test_imports.py")
        print_success("Installation test passed!")
    else:
        print_error("Installation test failed")
        return False
    
    # Step 8: Display next steps
    print(f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                              SETUP COMPLETE!                                ║
╚══════════════════════════════════════════════════════════════════════════════╝

Next Steps:

1. Configure your .env file:
   - Add your exchange API keys
   - Configure database connections
   - Set up notification services

2. Install Ollama models:
   - ollama pull llama3.2:3b
   - ollama pull qwen2.5:7b
   - ollama pull gemma2:9b
   - ollama pull mistral:7b

3. Start the databases:
   - docker-compose up -d postgres redis

4. Run the system:
   - {python_cmd} main.py

5. Access the web interface:
   - http://localhost:8000

Important Notes:
- Some packages may need manual installation if they failed
- Configure your exchange API keys before trading
- Start with paper trading mode for testing
- For advanced technical analysis, install TA-Lib manually

Happy Trading! 🚀
    """)
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1) 